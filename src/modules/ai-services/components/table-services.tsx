"use client";
import { ServiceGrid } from "./service-grid";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import AppLoading from "@/components/common/app-loading";
import { useGetAllService } from "@/hooks/model/useGetAllService";

const TableService: React.FC = () => {
  const { requestGetAllService, data, loading } = useGetAllService();
  useEffect(() => {
    requestGetAllService({});
  }, []);
  return (
    <>
      <div className="flex flex-col gap-5">
        {loading && <AppLoading className={"text-white"} />}
        <p className="text-34-34 font-semibold">AI Services</p>
        <hr />
        {!data.length ? <div></div> : <ServiceGrid listServices={data} />}
      </div>
    </>
  );
};

export default TableService;
