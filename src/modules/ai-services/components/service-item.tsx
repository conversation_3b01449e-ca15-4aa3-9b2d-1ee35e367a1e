"use client";

import { ModalCommon } from "@/components/common/modal-common";
import { useState } from "react";
import { Card } from "antd";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

export interface ServiceItemProps {
  service: any;
}

export function ServiceItem({ service }: ServiceItemProps) {
  const router = useRouter();

  const { Meta } = Card;
  return (
    <>
      <Card
        hoverable
        style={{ width: 250 }}
        onClick={() =>
          router.push(`/ai-services/${service.name.toLowerCase()}`)
        }
        cover={<img alt="example" src={service.thumbnail} />}
      >
        <Meta title={service.name} description={service.title} />
        <div>
          <p className="text-info pt-10">Try it out</p>
        </div>
      </Card>
    </>
  );
}
