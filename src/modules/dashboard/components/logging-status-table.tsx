import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import { IconSearch } from "@/components/icons";
import { ModalDrillhole } from "@/modules/drillhole/components/modal-drillhole";
import loggingViewApi from "@/modules/logging-view/api/logging-view.api";
import { EyeOutlined } from "@ant-design/icons";
import { Link } from "@mui/material";
import { Select, Tag, Tooltip } from "antd";
import { createStyles } from "antd-style";
import { useEffect, useState } from "react";
import { debounce } from "lodash";
import { useRouter } from "next/navigation";
import { FaRegEdit } from "react-icons/fa";
import { drillHoleStatusOptions } from "@/modules/drillhole/model/enum/drillhole.enum";
import { Option } from "antd/es/mentions";
export function LoggingStatusTable() {
  const [isLoading, setIsLoading] = useState(false);
  const [loggingData, setLoggingData] = useState<any[]>([]);
  const [columnsLogging, setColumnsLogging] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });
  const [sortField, setSortField] = useState<
    "name" | "creationTime" | "drillHoleStatus" | "project"
  >("creationTime");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [keyword, setKeyword] = useState("");
  const [drillholeSelected, setDrillholeSelected] = useState<any>(null);
  const globalProjectId = useAppSelector(
    (state) => state.user.userInfo.projectId
  );
  const router = useRouter();

  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo.prospectId
  );
  const useStyle = createStyles(({ token, css, cx }) => {
    return {
      customTable: css`
        .ant-table {
          .ant-table-container {
            .ant-table-body,
            .ant-table-content {
              scrollbar-width: thin;
              scrollbar-color: #eaeaea transparent;
              scrollbar-gutter: stable;
              max-height: 545px;
            }
          }
        }
      `,
    };
  });
  const styles = useStyle();
  const fetchLoggingData = async ({
    maxResultCount,
    skipCount,
    sortField,
    sortOrder,
    Keyword,
    drillHoleStatus,
  }) => {
    setIsLoading(true);
    const response = await loggingViewApi.getDrillholeLogging({
      projectId: globalProjectId as number,
      prospectId: globalProspectId as number,
      skipCount,
      maxResultCount,
      sortField,
      sortOrder,
      keyword: Keyword,
      drillHoleStatus,
    });
    let newData = response?.data;
    setTotal(newData?.totalCount);

    if (!newData) {
      setIsLoading(false);
      return;
    }

    // Transform the data
    const transformedData = newData.items?.map((item: any) => {
      // Start with the fixed fields
      const transformed: any = {
        name: item.name,
        project: item.project,
        prospect: item.prospect,
      };

      // Add all geology suites
      if (item.geologySuites) {
        Object.entries(item.geologySuites).forEach(([key, value]) => {
          transformed[key] = value;
        });
      }

      // Add all assay suites
      if (item.assaySuites) {
        Object.entries(item.assaySuites).forEach(([key, value]) => {
          transformed[key] = value;
        });
      }

      // Add all geophysics suites
      if (item.geophysicsSuites) {
        Object.entries(item.geophysicsSuites).forEach(([key, value]) => {
          transformed[key] = value;
        });
      }

      // Add all geotech suites
      if (item.geotechSuites) {
        Object.entries(item.geotechSuites).forEach(([key, value]) => {
          transformed[key] = value;
        });
      }

      // Add all calculation tabs
      if (item.calculationTabs) {
        Object.entries(item.calculationTabs).forEach(([key, value]) => {
          transformed[key] = value;
        });
      }

      return transformed;
    });
    setLoggingData(transformedData);
    if (transformedData.length === 0) {
      setIsLoading(false);
      return;
    }
    let keys = Object.keys(transformedData[0]);

    let newKeys = keys.filter(
      (key) => key !== "name" && key !== "project" && key !== "prospect"
    );
    let col: any[] = [
      {
        title: "Action",
        dataIndex: "action",
        key: "action",
        width: 100,
        render: (_, record, index) => {
          return (
            <div className="flex gap-3 items-center">
              <Tooltip title="View Images">
                <Link href={`/images?name=${record.name}`}>
                  <EyeOutlined className="h-8" />
                </Link>
              </Tooltip>
              <Tooltip title="View Logging">
                <Link href={`/logging?drillholeId=${record.id}`}>
                  <FaRegEdit />
                </Link>
              </Tooltip>
            </div>
          );
        },
      },
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
        width: 100,
        sorter: true,
        render(value, record, index) {
          return (
            <p
              onClick={() => {
                router.push(`/drillhole-management/${record.id}`);
              }}
              className="cursor-pointer"
            >
              {value}
            </p>
          );
        },
      },
      {
        title: "Project",
        dataIndex: "project",
        key: "project",
        width: 100,
        sorter: true,
        render: (value, record, index) => (
          <Tag
            color={record?.project.backgroundColor}
            className={`min-w-[100px]`}
            style={{ color: record?.project.textColor }}
            key={index}
          >
            {value?.name}
          </Tag>
        ),
      },
      {
        title: "Prospect",
        dataIndex: "prospect",
        key: "prospect",
        width: 100,
        render: (value, record, index) => (
          <Tag
            style={{
              backgroundColor: record?.prospect?.backgroundColor,
              color: record?.prospect?.textColor,
            }}
          >
            {record?.prospect?.name}
          </Tag>
        ),
      },
    ];
    newKeys.forEach((key) => {
      col.push({
        title: key,
        dataIndex: key,
        width: 120,
        render: (value: any) => {
          if (value === true) return "Yes";
          if (value === false) return "No";
          if (value === null) return "-";
          if (value?.assayCount !== undefined) {
            return `${value?.startDepth.toFixed(
              2
            )}m - ${value?.endDepth.toFixed(2)}m | ${value?.assayCount.toFixed(
              2
            )} assay`;
          }
          if (value?.hasOverlap !== undefined)
            return `${value?.startDepth.toFixed(
              2
            )}m - ${value?.endDepth.toFixed(2)}m | ${
              value?.hasOverlap ? "Has Overlap" : "No Overlap"
            } | ${value?.hasGap ? "Has Gap" : "No Gap"}`;
          return JSON.stringify(value);
        },
      });
    });
    setColumnsLogging(col);
    setIsLoading(false);
  };
  const pageSize = 50;
  const [currentPage, setCurrentPage] = useState(1);
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === "ascend" ? "asc" : "desc");
    }
  };

  useEffect(() => {
    fetchLoggingData({
      maxResultCount: pageSize,
      skipCount: 0,
      sortField: sortField,
      sortOrder: sortOrder,
      Keyword: keyword,
      drillHoleStatus: drillholeSelected,
    });
  }, [sortField, sortOrder, keyword, drillholeSelected]);
  const handleSearch = debounce((value: string) => {
    setKeyword(value);
    setCurrentPage(1);
  }, 1500);

  return (
    <>
      {modalState.isOpen && (
        <ModalDrillhole
          fetchListDrillhole={fetchLoggingData}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex justify-between gap-5">
        <div className="flex gap-5">
          <div className=" px-5 rounded-lg flex items-center gap-2 h-[38px] bg-white border">
            <IconSearch />
            <input
              type="text"
              placeholder={`Search by name`}
              className="w-[200px] font-normal font-dmSans outline-none text-primary placeholder:text-gray80"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          <div className=" flex gap-2">
            <p className="font-medium min-w-16 mt-2">Status</p>
            <Select
              value={drillholeSelected}
              onChange={(value) => {
                setDrillholeSelected(value as any);
                setCurrentPage(1);
              }}
              className="w-full h-full min-h-[38px]"
              placeholder={`Select a status`}
              filterOption={false}
              allowClear
            >
              {drillHoleStatusOptions.map((d: any) => (
                <Option key={d.value} value={d.value}>
                  {d.label}
                </Option>
              ))}
            </Select>
          </div>
        </div>
      </div>
      <TableCommon
        className={styles.styles.customTable}
        columns={columnsLogging}
        dataSource={loggingData}
        loading={isLoading}
        scroll={{ x: "max-content", y: 6 * 85 }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page) => setCurrentPage(page),
          // total,
        }}
        onChange={handleTableChange}
        footer={() => (
          <div className="justify-center my-2 flex flex-col gap-2">
            <Link
              className="btn w-full bg-primary border-none hover:bg-primary-hover text-white"
              href="/drillhole-management"
            >
              See all
            </Link>
            <p className=" text-gray-500 text-center">
              Showing {loggingData?.length} records of {total}
            </p>
          </div>
        )}
      />
    </>
  );
}
