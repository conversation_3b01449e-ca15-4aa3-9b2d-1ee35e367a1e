"use client";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import DrillHoleStatus from "@/components/common/drillhole-status";
import { TableCommon } from "@/components/common/table-common";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import { EyeOutlined } from "@ant-design/icons";
import { Line, Pie } from "@ant-design/plots";
import {
  DatePicker,
  Radio,
  RadioChangeEvent,
  TableColumnsType,
  TabsProps,
  Tag,
  Tooltip,
} from "antd";
import { createStyles } from "antd-style";
import Link from "next/link";
import { useEffect, useState } from "react";
import { FaRegEdit } from "react-icons/fa";
import { LoggingStatusTable } from "./logging-status-table";
import { RecentDrillholeTable } from "./recent-drillhole-table";

export function DrillholeChart() {
  const drillHoleStatusData = [
    { status: "Not started", value: 145 },
    { status: "In progress", value: 230 },
    { status: "Reprocess", value: 85 },
    { status: "Review", value: 175 },
    { status: "Complete", value: 320 },
    { status: "Exported", value: 250 },
    { status: "Unnamed", value: 45 },
  ];

  const pieConfig = {
    data: drillHoleStatusData,
    angleField: "value",
    colorField: "status",
    radius: 0.8,
    label: {
      text: "value",
      style: {
        fontWeight: "bold",
      },
    },
    interactions: [
      {
        type: "element-active",
      },
    ],
    title: {
      text: "Phân bố trạng thái Drill Hole",
      style: {
        fontSize: 18,
      },
    },
    legend: {
      color: {
        title: false,
        position: "right",
        rowPadding: 5,
      },
    },
  };
  // Tạo fake data cho 7 ngày gần nhất
  const data = [
    { date: "2024-03-20", count: 15 },
    { date: "2024-03-21", count: 25 },
    { date: "2024-03-22", count: 18 },
    { date: "2024-03-23", count: 30 },
    { date: "2024-03-24", count: 22 },
    { date: "2024-03-25", count: 28 },
    { date: "2024-03-26", count: 35 },
  ];

  const config = {
    data,
    xField: "date",
    yField: "count",
    smooth: true,
    // Tùy chỉnh style cho line chart
    // style: {
    //   lineWidth: 3,
    // },
    // Tùy chỉnh tooltip
    tooltip: {
      formatter: (datum: any) => {
        return { name: "Số lượng", value: datum.count };
      },
    },
    // Tùy chỉnh label trên trục y
    yAxis: {
      title: {
        text: "Số lượng Drillhole",
      },
    },
    // Tùy chỉnh label trục x
    xAxis: {
      title: {
        text: "Ngày",
      },
    },
  };
  const onChange = (key: string) => {
    console.log(key);
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Recent Drillholes",
      children: <RecentDrillholeTable />,
    },
    {
      key: "4",
      label: "Logging Status",
      children: <LoggingStatusTable />,
    },
    {
      key: "2",
      label: "Status",
      children: <Pie {...pieConfig} />,
    },
    {
      key: "3",
      label: "Drillhole Chart",
      children: (
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-center">
            <div className="text-lg font-bold text-2xl">Drillhole Chart</div>
            <div className="flex gap-2 items-center">
              <p className="text-sm font-bold">From</p>
              <DatePicker />
              <p className="text-sm font-bold">To</p>
              <DatePicker />
            </div>
          </div>
          <Line {...config} />
        </div>
      ),
    },
  ];
  const [mode, setMode] = useState<any>("1");

  const handleModeChange = (e: RadioChangeEvent) => {
    setMode(e.target.value);
  };
  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col gap-2">
        <div className="text-lg font-bold text-2xl">Drillhole</div>
        <Radio.Group
          onChange={handleModeChange}
          value={mode}
          style={{ marginBottom: 8 }}
        >
          {items?.map((item) => {
            return (
              <Radio.Button key={item.key} value={item.key}>
                {item.label}
              </Radio.Button>
            );
          })}
        </Radio.Group>
        {items?.find((item) => item.key === mode)?.children}
      </div>
    </div>
  );
}
