"use client";
import { useGetListUserAdmin } from "@/modules/account-management/hooks/useGetListUserAdmin.hook";
import { TableCommon } from "@/components/common/table-common";
import { AccountBodyType } from "@/modules/account/model/schema/account.schema";
import { CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { DatePicker, TableColumnsType, Tabs, TabsProps, Tag } from "antd";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import dynamic from "next/dynamic";

// Dynamically import the Line chart to reduce initial bundle size
const Line = dynamic(
  () => import("@ant-design/charts").then((mod) => ({ default: mod.Line })),
  {
    ssr: false,
    loading: () => (
      <div className="h-64 flex items-center justify-center">
        Loading chart...
      </div>
    ),
  }
);
export function UserChart() {
  const [requestGetListUserAdmin] = useGetListUserAdmin();
  const [dataUser, setDataUser] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    requestGetListUserAdmin(
      {
        isActive: true,
        skipCount: 0,
        maxResultCount: 3,
      },
      setLoading,
      (res) => {
        setDataUser(res?.result?.users);
      },
      (error) => {
        console.log(error);
      }
    );
  }, []);
  const data = [
    { month: "01/2024", users: 150 },
    { month: "02/2024", users: 280 },
    { month: "03/2024", users: 320 },
    { month: "04/2024", users: 450 },
    { month: "05/2024", users: 380 },
    { month: "06/2024", users: 520 },
    { month: "07/2024", users: 610 },
    { month: "08/2024", users: 780 },
    { month: "09/2024", users: 850 },
    { month: "10/2024", users: 920 },
    { month: "11/2024", users: 1050 },
    { month: "12/2024", users: 1200 },
  ];

  const config = {
    data,
    height: 400,
    xField: "month",
    yField: "users",
    point: {
      size: 5,
      shape: "diamond",
    },
    label: {
      style: {
        fill: "#aaa",
      },
    },
    title: {
      text: "Thống kê người dùng theo tháng năm 2024",
      style: {
        fontSize: 18,
      },
    },
  };
  const columnsAdmin: TableColumnsType<AccountBodyType> = [
    {
      title: "Email",
      dataIndex: "emailAddress",
      key: "email",
    },
    {
      title: "First Name",
      dataIndex: "firstName",
      key: "firstName",
    },
    {
      title: "Last Name",
      dataIndex: "lastName",
      key: "lastName",
    },
    {
      title: "Account",
      dataIndex: "companyName",
      key: "companyName",
      render: (value, record, index) => {
        return (
          <div className="flex gap-2 ">
            <span className="capitalize">{value}</span>
          </div>
        );
      },
    },

    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      render: (status) =>
        status ? (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
          >
            Active
          </Tag>
        ) : (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Inactive
          </Tag>
        ),
    },
  ];
  const fontSize = useAppSelector((state) => state.user.fontSize);
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Recent Users",
      children: (
        <TableCommon
          style={{
            fontSize: `${fontSize}px`,
          }}
          loading={loading}
          columns={columnsAdmin as any}
          dataSource={dataUser}
          footer={() => {
            return (
              <div className="justify-center my-2 ">
                <Link
                  className="btn w-full bg-primary border-none hover:bg-primary-hover text-white"
                  href="/account-management"
                >
                  See all
                </Link>
              </div>
            );
          }}
        />
      ),
    },
    {
      key: "2",
      label: "User Chart",
      children: (
        <>
          <div className="flex justify-between items-center">
            <div className="text-lg font-bold text-2xl">User Chart</div>
            <div className="flex gap-2 items-center">
              <p className="text-sm font-bold">From</p>
              <DatePicker />
              <p className="text-sm font-bold">To</p>
              <DatePicker />
            </div>
          </div>
          <Line {...config} />
        </>
      ),
    },
  ];
  const onChange = (key: string) => {
    console.log(key);
  };
  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col gap-2">
        <div className="text-lg font-bold text-2xl">User Chart</div>
        <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
      </div>
    </div>
  );
}
