import DrillHoleStatus from "@/components/common/drillhole-status";
import { TableCommon } from "@/components/common/table-common";
import { IconSearch } from "@/components/icons";

import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import { drillHoleStatusOptions } from "@/modules/drillhole/model/enum/drillhole.enum";
import { EyeOutlined } from "@ant-design/icons";
import { Link } from "@mui/material";
import { Select, TableColumnsType, Tag, Tooltip } from "antd";
import { createStyles } from "antd-style";
import { Option } from "antd/es/mentions";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FaRegEdit } from "react-icons/fa";

export function RecentDrillholeTable() {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 50;
  const {
    data: drillholes,
    request: getDrillholes,
    loading: isLoadingDrillholes,
    total,
  } = useGetListDrillhole();
  const [sortField, setSortField] = useState<
    | "name"
    | "creationTime"
    | "drillHoleStatus"
    | "project"
    | "prospect"
    | "croppedRows"
    | "maxDepth"
  >("creationTime");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [keyword, setKeyword] = useState("");
  const [drillholeSelected, setDrillholeSelected] = useState<any>(null);
  useEffect(() => {
    getDrillholes({
      maxResultCount: pageSize,
      skipCount: (currentPage - 1) * pageSize,
      sortField: sortField,
      sortOrder: sortOrder,
      isActive: true,
      keyword,
      drillHoleStatus: drillholeSelected,
    });
  }, [currentPage, sortField, sortOrder, keyword, drillholeSelected]);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === "ascend" ? "asc" : "desc");
    }
  };
  const router = useRouter();

  const columns: TableColumnsType<any> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      width: 100,
      render: (_, record, index) => {
        return (
          <div className="flex gap-3 items-center">
            <Tooltip title="View Images">
              <Link href={`/images?name=${record.name}`}>
                <EyeOutlined className="h-8" />
              </Link>
            </Tooltip>
            <Tooltip title="View Logging">
              <Link href={`/logging?drillholeId=${record.id}`}>
                <FaRegEdit />
              </Link>
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 200,
      sorter: true,
      render(value, record, index) {
        return (
          <p
            onClick={() => {
              router.push(`/drillhole-management/${record?.id}`);
            }}
            className="cursor-pointer"
          >
            {value}
          </p>
        );
      },
    },
    {
      title: "Project",
      dataIndex: "project",
      key: "project",
      sorter: true,
      width: 200,
      render: (value, record, index) => (
        <Tag
          color={record?.project.backgroundColor}
          className={`min-w-[100px]`}
          style={{ color: record?.project.textColor }}
          key={index}
        >
          {value?.name}
        </Tag>
      ),
    },
    {
      title: "Prospect",
      dataIndex: "prospect",
      key: "prospect",
      width: 200,

      render: (value, record, index) => (
        <Tag
          style={{
            backgroundColor: record?.prospect?.backgroundColor,
            color: record?.prospect?.textColor,
          }}
        >
          {record?.prospect?.name}
        </Tag>
      ),
    },

    {
      title: "Status",
      dataIndex: "drillHoleStatus",
      key: "drillHoleStatus",
      sorter: true,
      width: 100,

      render: (drillHoleStatus) => {
        return <DrillHoleStatus status={drillHoleStatus} />;
      },
    },
    {
      title: "Date",
      dataIndex: "creationTime",
      key: "creationTime",
      width: 200,
      sorter: true,
      render(value, record, index) {
        return new Date(value).toLocaleString();
      },
    },
    {
      title: "Original Images",
      dataIndex: "originalImages",
      width: 200,
    },
    {
      title: "Row Images",
      dataIndex: "croppedRows",
      sorter: true,
      width: 100,
    },

    {
      title: "Max Depth",
      dataIndex: "maxDepth",
      key: "maxDepth",
      sorter: true,
      width: 100,
    },
  ];
  const useStyle = createStyles(({ token, css, cx }) => {
    return {
      customTable: css`
        .ant-table {
          .ant-table-container {
            .ant-table-body,
            .ant-table-content {
              scrollbar-width: thin;
              scrollbar-color: #eaeaea transparent;
              scrollbar-gutter: stable;
              max-height: 545px;
            }
          }
        }
      `,
    };
  });
  const styles = useStyle();

  return (
    <div className="flex flex-col gap-5">
      <div className="flex justify-between gap-5">
        <div className="flex gap-5">
          <div className=" px-5 rounded-lg flex items-center gap-2 h-[38px] bg-white border">
            <IconSearch />
            <input
              type="text"
              placeholder={`Search by name`}
              className="w-[200px] font-normal font-dmSans outline-none text-primary placeholder:text-gray80"
              onChange={(e) => {
                setKeyword(e.target.value);
                setCurrentPage(1);
              }}
            />
          </div>
          <div className=" flex gap-2">
            <p className="font-medium min-w-16 mt-2">Status</p>
            <Select
              value={drillholeSelected}
              onChange={(value) => {
                setDrillholeSelected(value as any);
                setCurrentPage(1);
              }}
              className="w-full h-full min-h-[38px]"
              placeholder={`Select a status`}
              filterOption={false}
              allowClear
            >
              {drillHoleStatusOptions.map((d: any) => (
                <Option key={d.value} value={d.value}>
                  {d.label}
                </Option>
              ))}
            </Select>
          </div>
        </div>
      </div>
      <TableCommon
        className={styles.styles.customTable}
        loading={isLoadingDrillholes}
        columns={columns as any}
        dataSource={drillholes}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page) => setCurrentPage(page),
          total,
        }}
        scroll={{ x: "max-content", y: 6 * 85 }}
        onChange={handleTableChange}
        footer={() => (
          <div className="justify-center my-2 flex flex-col gap-2">
            <Link
              className="btn w-full bg-primary border-none hover:bg-primary-hover text-white"
              href="/drillhole-management"
            >
              See all
            </Link>
            <p className=" text-gray-500 text-center">
              Showing {drillholes?.length} records of {total}
            </p>
          </div>
        )}
      />
    </div>
  );
}
