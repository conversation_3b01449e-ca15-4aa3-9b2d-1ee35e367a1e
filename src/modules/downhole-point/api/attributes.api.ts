import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import http from "@/lib/http";
import { getErrorMessage } from "@/utils/error.utils";
import { AttributeBodyType } from "../schemaValidator/attribute.schema";

const attributeRequest = {
  getList: async (params: {
    keyword?: string;
    maxResultCount: number;
    skipCount: number;
    isActive?: boolean;
  }) => {
    try {
      return http.get(`/services/app/Attribute/GetAll`, {
        params,
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  assign: async (body: { suiteId: string; attributeIds: any[] }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Suite/AssignAttribute`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getAttributeBySuiteId: async (body: { Id: string }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Attribute/GetAttributesBySuiteId`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  create: async (body: AttributeBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Attribute/Create`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  delete: async (params: { id: string }) => {
    try {
      return http.delete(`/services/app/Attribute/Delete`, {
        params,
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },

  update: async (body: AttributeBodyType) => {
    try {
      return http.put(`/services/app/Attribute/Update`, body);
    } catch (error) {
      return Promise.reject(error);
    }
  },
  getAttributeByProjectId: async (params: {
    id: string;
    drillHole: string;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Attribute/GetAttributesByProjectId`,
        params
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        result: error.result,
      };
    }
  },
};

export default attributeRequest;
