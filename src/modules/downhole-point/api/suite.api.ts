import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import http from "@/lib/http";
import { getErrorMessage } from "@/utils/error.utils";
import { SuiteQuery } from "../interface/suite.query";

const suiteRequest = {
  getList: async (params: SuiteQuery) => {
    try {
      return http.get(`/services/app/Suite/GetAll`, {
        params,
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },

  getSuiteByProjectId: async (params: { Id?: number }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Suite/GetSuiteByProjectId`,
        params
      );

      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getSuiteByLoggingViewId: async (params: {
    loggingViewId?: number;
    skipCount?: number;
    maxResultCount?: number;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Suite/GetAllByLoggingView`,
        params
      );

      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getAttributes: async (params: { id?: number }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Attribute/GetAttributesBySuiteId`,
        params
      );

      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  create: async (body: any) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Suite/Create`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  delete: async (params: { id: string }) => {
    try {
      return http.delete(`/services/app/Suite/Delete`, {
        params,
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },

  update: async (body: any) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/Suite/Update`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getDetail: async (params: { Id: number }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Suite/Get?Id=${params.Id}`,
        params
      );

      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default suiteRequest;
