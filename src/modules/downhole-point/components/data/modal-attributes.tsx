import { ModalType } from "@/common/configs/app.enum";
import { ButtonCommon } from "@/components/common/button-common";
import ColorPickerCommon from "@/components/common/color-picker";
import { InputNumberCommon } from "@/components/common/input-number";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateAttribute } from "../../hooks/useCreateAttribute.hook";
import { useDeleteAttribute } from "../../hooks/useDeleteAttribute.hook";
import { useUpdateAttribute } from "../../hooks/useUpdateAttribute.hook";
import { AttributeBody } from "../../schemaValidator/attribute.schema";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalAttribute(props: IModalCompanyProps) {
  const { modalState, setModalState, refresh } = props;
  const { request: requestCreateAttribute, loading: loadingCreateAttribute } =
    useCreateAttribute();
  const { request: requestUpdateAttribute, loading: loadingUpdateAttribute } =
    useUpdateAttribute();
  const { request: requestDeleteAttribute, loading: loadingDeleteAttribute } =
    useDeleteAttribute();
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };

  const { control, handleSubmit, setValue, getValues } = useForm<any>({
    resolver: zodResolver(AttributeBody),
    defaultValues: {
      name: modalState?.detailInfo?.name,
      code: modalState?.detailInfo?.code,
      backgroundColor: modalState?.detailInfo?.backgroundColor || "#000000",
      textColor: modalState?.detailInfo?.textColor || "#FFFFFF",
      minInterval: modalState?.detailInfo?.minInterval,
      minValue: modalState?.detailInfo?.minValue,
      maxValue: modalState?.detailInfo?.maxValue,
      isActive: modalState?.detailInfo?.isActive ?? true,
      id: modalState?.detailInfo?.id,
    },
  });

  const isConfirm = modalState.type === ModalType.DELETE;

  const onSubmit = (values: any) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateAttribute(
        values,
        (res: any) => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create attribute successfully");
          refresh();
        },
        () => {}
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateAttribute(
        values,
        (res: any) => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update attribute successfully");
          refresh();
        },
        () => {}
      );
    }
  };

  const handleDelete = () => {
    requestDeleteAttribute(
      { id: modalState.detailInfo.id },
      (res: any) => {
        setModalState({ ...modalState, isOpen: false });
        toast.success("Delete attribute successfully");
        refresh();
      },
      () => {}
    );
  };
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this attribute?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            attribute
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteAttribute}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update an attribute"
              : "Create an attribute"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type Attribute name here"
              control={control}
            />
            <InputTextCommon
              label="Code"
              name="code"
              placeholder="Type Attribute code here"
              control={control}
            />

            <InputNumberCommon
              label="Minimum Interval"
              name="minInterval"
              placeholder="Type Attribute min interval here"
              control={control}
            />

            <InputNumberCommon
              label="Minimum Value"
              name="minValue"
              placeholder="Type Attribute minimun value here"
              control={control}
            />
            <InputNumberCommon
              label="Maximum Value"
              name="maxValue"
              placeholder="Type Attribute maximum value here"
              control={control}
            />

            <ColorPickerCommon
              getValues={getValues}
              setValue={setValue}
              control={control}
              name="backgroundColor"
              label="Background Color"
              placeholder="Choose background color here"
            />
            <ColorPickerCommon
              setValue={setValue}
              getValues={getValues}
              control={control}
              name="textColor"
              label="Text Color"
              placeholder="Choose text color here"
            />
            <ToogleCommon label="Active" control={control} name="isActive" />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                loading={loadingCreateAttribute || loadingUpdateAttribute}
                type="submit"
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update Attribute"
                  : "Create Attribute"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
