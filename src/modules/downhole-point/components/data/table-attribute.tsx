"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import {
  AppstoreOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import { TableColumnsType, Tag, Tooltip } from "antd";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { selectAttribute } from "../../redux/attributeSlice";
import { getAttributes } from "../../redux/attributeSlice/thunks";
import { AttributeBodyType } from "../../schemaValidator/attribute.schema";
import { ModalAttribute } from "./modal-attributes";
let timeout: any;

const DataAttribute = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const attributes = useAppSelector(selectAttribute);
  const { replace } = useRouter();
  const pathname = usePathname();

  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });

  //state
  const [keyword, setKeyword] = useState<string | undefined>(
    searchParams.get("Keyword")?.toString()
  );

  //table
  const columns: TableColumnsType<AttributeBodyType> = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
    },
    {
      title: "Minimum Interval",
      dataIndex: "minInterval",
      key: "minInterval",
    },
    {
      title: "Min Value",
      dataIndex: "minValue",
      key: "minValue",
    },
    {
      title: "Max Value",
      dataIndex: "maxValue",
      key: "maxValue",
    },
    {
      title: "Background Color",
      dataIndex: "backgroundColor",
      key: "backgroundColor",
      render: (value, record, index) => (
        <Tag
          color={value}
          className={`min-w-[100px]`}
          style={{ color: record.textColor }}
          key={index}
        >
          {value}
        </Tag>
      ),
    },
    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      render: (status) =>
        status ? (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
          >
            Active
          </Tag>
        ) : (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Inactive
          </Tag>
        ),
    },

    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3">
            <Tooltip title="Edit attribute">
              <EditOutlined
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "update",
                    detailInfo: record,
                  })
                }
                style={{ fontSize: 16 }}
                className="hover:text-primary cursor-pointer"
              />
            </Tooltip>

            <Tooltip title="Delete attribute">
              <DeleteOutlined
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "delete",
                    detailInfo: record,
                  })
                }
                className="hover:text-primary cursor-pointer"
                style={{ fontSize: 16 }}
              />
            </Tooltip>

            <Tooltip title="View point data">
              <AppstoreOutlined
                onClick={() => router.push(`/attributes/data`)}
                style={{ fontSize: 16 }}
                className="hover:text-primary cursor-pointer"
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const buttons = [
    {
      title: "All",
      isActveString: undefined,
      onclick: () => {
        const params = new URLSearchParams(queries);
        params.delete("isActive");
        params.delete("page");
        params.delete("pageSize");
        params.delete("keyword");
        router.push(`${window.location.pathname}?${params.toString()}`);
      },
    },
    {
      title: "Active",
      isActveString: "true",
      onclick: () => {
        const params = new URLSearchParams(queries);
        params.set("isActive", "true");
        params.delete("page");
        params.delete("pageSize");
        params.delete("keyword");
        router.push(`${window.location.pathname}?${params.toString()}`);
      },
    },
    {
      title: "Inactive",
      isActveString: "false",
      onclick: () => {
        const params = new URLSearchParams(queries);
        params.set("isActive", "false");
        params.delete("page");
        params.delete("pageSize");
        params.delete("keyword");
        router.push(`${window.location.pathname}?${params.toString()}`);
      },
    },
    // {
    //   title: "Add Attributes",
    //   isActveString: "false",
    //   icon: <PlusOutlined />,
    //   onclick: () => {
    //     setModalState({
    //       ...modalState,
    //       isOpen: true,
    //       type: "create",
    //       detailInfo: undefined,
    //     });
    //   },
    // },
  ];

  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: attributes?.result?.result?.items ?? [],
      reduxTablePagination: {
        total: attributes?.result?.result?.totalCount ?? 0,
      } as any,
      requestState: attributes?.status ?? RequestState.idle,
      getDataAction: getAttributes,
      filter: {
        page: 1,
        pageSize: 10,
      },
    });

  const handleSearch = (term: string) => {
    setKeyword(term);
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      const params = new URLSearchParams(searchParams);
      if (term) {
        params.set("Keyword", term);
      } else {
        params.delete("Keyword");
      }
      replace(`${pathname}?${params.toString()}`);
    }, 500);
  };

  return (
    <>
      {modalState?.isOpen && (
        <ModalAttribute
          refresh={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <div className="flex justify-between">
          <p className="text-34-34 font-semibold">Attributes</p>
          <div className="flex gap-2">
            <button className={`btn btn-sm btn-purple`} onClick={() => {}}>
              Attributes
            </button>
            <button
              className={`btn btn-sm`}
              onClick={() => {
                router.push("/suites");
              }}
            >
              Suites
            </button>
          </div>
        </div>
        <hr />
        <div className="flex justify-between items-center">
          <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[400px] bg-white border">
            <IconSearch />
            <input
              type="text"
              placeholder="Search"
              className="w-full font-normal outline-none text-primary placeholder:text-gray80"
              onChange={(e) => {
                handleSearch(e.target.value);
              }}
              value={keyword}
            />
          </div>
          <div className="flex justify-between gap-2">
            <div className="flex gap-2">
              {buttons.map((button, index) => {
                let className: string = "";
                const isActiveSearchParam = queries?.isActive;
                if (isActiveSearchParam === null && button.title === "All") {
                  className = "btn-primary btn-active";
                }
                if (button.title === "Add Attributes") {
                  // className = "btn-purple";
                }
                if (isActiveSearchParam === button.isActveString) {
                  className = "btn-primary btn-active";
                }
                return (
                  <button
                    key={index}
                    className={`btn btn-sm ${className} `}
                    onClick={button.onclick}
                  >
                    {button.title}
                  </button>
                );
              })}
              <button
                className={`btn btn-sm btn-purple `}
                onClick={() => {
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  });
                }}
              >
                <PlusOutlined />
                Add Attributes
              </button>
            </div>
          </div>
        </div>
        <TableCommon
          className="font-visby"
          pagination={tablePagination}
          loading={attributes.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={attributes?.result?.result?.items ?? []}
        />
      </div>
    </>
  );
};

export default DataAttribute;
