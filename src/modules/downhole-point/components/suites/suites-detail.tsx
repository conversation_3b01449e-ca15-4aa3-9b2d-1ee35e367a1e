"use client";
import { TableCommon } from "@/components/common/table-common";
import { Tag } from "antd";
import type { TablePaginationConfig } from "antd/es/table/interface";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useEffect, useState } from "react";
import { useGetListAttributeBySuiteId } from "../../hooks/useGetAttributeBySuiteId";
let timeout: any;

export function SuiteDetail() {
  const router = useRouter();
  const params = useParams();
  const id = params.id[0];
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });

  //state
  const [total, setTotal] = useState(0);
  const [keyword, setKeyword] = useState<string | undefined>(
    searchParams.get("Keyword")?.toString()
  );
  const [loading, setLoading] = useState(false);

  const { data, request: requestGetListAttributeBySuiteId } =
    useGetListAttributeBySuiteId();
  useEffect(() => {
    requestGetListAttributeBySuiteId({ Id: id });
  }, []);

  const columnsSystemAdmin = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
    },
    {
      title: "Min Interval",
      dataIndex: "minInterval",
      key: "minInterval",
    },
    {
      title: "Minimum Value",
      dataIndex: "minValue",
      key: "minValue",
    },
    {
      title: "Maximum Value",
      dataIndex: "maxValue",
      key: "maxValue",
    },
    {
      title: "Background Color",
      dataIndex: "backgroundColor",
      key: "backgroundColor",
      render: (value, record, index) => (
        <Tag
          color={value}
          className={`min-w-[100px]`}
          style={{ color: record.textColor }}
          key={index}
        >
          {value}
        </Tag>
      ),
    },
  ];

  const handleTableChange = (pagination: TablePaginationConfig) => {
    const params = new URLSearchParams(searchParams);
    params.set(
      "skipCount",
      `${((pagination.current || 1) - 1) * maxResultCount}`
    );
    params.set("maxResultCount", maxResultCount.toString());
    replace(`${pathname}?${params.toString()}`);
  };

  let skipCount = Number(searchParams.get("skipCount")) || 1;
  let maxResultCount = Number(searchParams.get("maxResultCount")) || 10;
  let current = Math.ceil(skipCount / maxResultCount);
  if (skipCount % maxResultCount === 0) {
    current += 1;
  }

  return (
    <>
      <div className="flex flex-col gap-5">
        <div className="flex justify-between">
          <p className="text-34-34 font-semibold">
            Downhole Point Data Attribute
          </p>
          <div className="flex gap-2">
            <button
              className={`btn btn-sm `}
              onClick={() => {
                router.push("/attributes");
              }}
            >
              Attributes
            </button>
            <button className={`btn btn-sm btn-purple`}>Suites</button>
          </div>
        </div>
        <hr />
        <div className="flex justify-start">
          <div className="text-24-28 font-bold">{`Suites ${id}`}</div>
        </div>
        <TableCommon
          className="font-visby"
          loading={loading}
          onChange={handleTableChange}
          columns={columnsSystemAdmin}
          dataSource={data}
        />
      </div>
    </>
  );
}
