import { ModalType } from "@/common/configs/app.enum";
import { RequestState } from "@/common/configs/app.contants";
import { SortOrder } from "@/common/interfaces/general/general.types";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { zodResolver } from "@hookform/resolvers/zod";
import { ButtonCommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { Form } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import suiteRequest from "../../api/suite.api";
import { useAssignSuite } from "../../hooks/useAssignSuite.hook";
import { useCreateSuite } from "../../hooks/useCreateSuite.hook";
import { useGetListAttributes } from "../../hooks/useGetListAttribute.hook";
import { selectSuite } from "../../redux/suiteSlice";
import { useUpdateSuite } from "../../hooks/useUpdateSuite";
import { useDeleteSuite } from "../../hooks/useDeleteSuite.hook";
import { useGetListProject } from "@/modules/projects/hooks";
import { SuiteBody, SuiteType } from "../../model/schema/suite-type.schema";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalAttribute(props: IModalCompanyProps) {
  const { modalState, setModalState, refresh } = props;
  const suites = useAppSelector(selectSuite);

  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };

  // Form for create/update suite
  const {
    control: suiteControl,
    handleSubmit: handleSuiteSubmit,
    setValue: setSuiteValue,
  } = useForm<SuiteType>({
    resolver: zodResolver(SuiteBody),
    defaultValues: {
      name: modalState?.detailInfo?.name,
      projectIds:
        modalState?.type === ModalType.UPDATE
          ? modalState?.detailInfo?.projects?.map(
              (project: any) => project.id
            ) || []
          : [],
    },
  });

  // Form for attribute assignments
  const {
    control: attributeControl,
    handleSubmit: handleAttributeSubmit,
    setValue: setAttributeValue,
  } = useForm<any>({});

  const [keywordProject, setKeywordProject] = useState("");
  const { data: projects, request: requestGetListProject } =
    useGetListProject();
  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  useEffect(() => {
    requestGetListProject({
      keyword: keywordProject,
      maxResultCount: maxResultCountProject,
      skipCount: 0,
      sortField: "name",
      sortOrder: SortOrder.ASC,
    });
  }, [keywordProject, maxResultCountProject]);

  useEffect(() => {
    if (modalState.type === ModalType.UPDATE && modalState.detailInfo?.id) {
      suiteRequest.getDetail({ Id: modalState.detailInfo.id }).then((res) => {
        if (res.state === RequestState.success) {
          setSuiteValue("name", res.data.name);
          setSuiteValue(
            "projectIds",
            res.data.projects?.map((project: any) => project.id) || []
          );
        }
      });
    }
  }, [modalState.type, modalState.detailInfo?.id]);
  const { request: requestCreateSuite, loading: loadingCreateSuite } =
    useCreateSuite();
  const { request: requestUpdateSuite, loading: loadingUpdateSuite } =
    useUpdateSuite();
  const { request: requestDeleteSuite, loading: loadingDeleteSuite } =
    useDeleteSuite();
  const { request: requestGetListAttribute } = useGetListAttributes();
  const [optionsAtrribute, setOptionsAtrribute] = useState<any[]>([]);
  useEffect(() => {
    requestGetListAttribute(
      {
        maxResultCount: 100,
        skipCount: 0,
      },
      (res: any) => {
        setOptionsAtrribute(
          res.result.items.map((item: any) => {
            return {
              value: item.id,
              label: item.name,
            };
          })
        );
      }
    );
  }, []);
  const { loading: loadingAssignSuite, request: requestAssignSuite } =
    useAssignSuite();

  const onSuiteSubmit = (value: SuiteType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateSuite(
        {
          name: value.name,
          projectIds: value.projectIds,
        },
        () => {
          toast.success("Create geophysic suite successfully");
          handleCancel();
          refresh();
        }
      );
    } else if (modalState.type === ModalType.UPDATE) {
      requestUpdateSuite(
        {
          id: modalState?.detailInfo?.id,
          name: value.name,
          projectIds: value.projectIds,
        },
        () => {
          toast.success("Update geophysic suite successfully");
          handleCancel();
          refresh();
        }
      );
    } else if (modalState.type === ModalType.DELETE) {
      requestDeleteSuite(
        {
          id: modalState?.detailInfo?.id,
        },
        () => {
          toast.success("Delete geophysic suite successfully");
          handleCancel();
          refresh();
        }
      );
    }
  };

  const onAttributeSubmit = (value: { attributeId: any[] }) => {
    requestAssignSuite(
      {
        attributeIds: value.attributeId,
        suiteId: modalState?.detailInfo?.id,
      },
      () => {
        toast.success("Assign attribute successfully");
        setModalState({ ...modalState, isOpen: false });
        handleCancel();
        refresh();
      },
      (error: any) => {
        console.log(error);

        toast.error(error ?? "Assign attribute failed");
      }
    );
  };

  useEffect(() => {
    if (!modalState?.detailInfo?.id || modalState.type !== ModalType.ASSIGN)
      return;
    suiteRequest
      .getAttributes({ id: modalState?.detailInfo?.id })
      .then((res) => {
        setAttributeValue(
          "attributeId",
          res.data.map((item: any) => item.id)
        );
      });
  }, [modalState?.detailInfo?.id, modalState?.type]);

  const handleSubmitForm =
    modalState.type === ModalType.ASSIGN
      ? handleAttributeSubmit
      : handleSuiteSubmit;

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      <div className="px-6 flex flex-col gap-4">
        <p className="font-bold text-24-28 capitalizefont-visby">
          {modalState.type === ModalType.CREATE && "Create a geophysic suite"}
          {modalState.type === ModalType.UPDATE && "Edit a geophysic suite"}
          {modalState.type === ModalType.DELETE && "Delete a geophysic suite"}
          {modalState.type === ModalType.ASSIGN &&
            "Assign geophysic attributes"}
        </p>
        <Form
          onFinish={
            modalState.type === ModalType.ASSIGN
              ? handleAttributeSubmit(onAttributeSubmit)
              : handleSuiteSubmit(onSuiteSubmit)
          }
          className="flex flex-col gap-3"
        >
          {modalState.type === ModalType.ASSIGN ? (
            <>
              <SelectCommon
                label="Attribute"
                control={attributeControl}
                name="attributeId"
                placeholder="Choose attributes"
                mode="multiple"
                allowClear
                options={optionsAtrribute}
              />
            </>
          ) : modalState.type === ModalType.DELETE ? (
            <p>
              Are you sure you want to delete{" "}
              <span className="font-bold">{modalState?.detailInfo?.name}</span>?
            </p>
          ) : (
            <>
              <SelectCommon
                label="Projects"
                onPopupScroll={handleScrollProject}
                name="projectIds"
                placeholder="Select projects"
                control={suiteControl}
                options={
                  projects?.map((item: any) => ({
                    label: item.name,
                    value: item.id,
                  })) || []
                }
                isRequired={true}
                mode="multiple"
                onChange={() => {
                  setKeywordProject("");
                }}
                searchValue={keywordProject}
                onSearch={(value) => {
                  setKeywordProject(value);
                }}
                onClear={() => {
                  setKeywordProject("");
                }}
                onBlur={() => {
                  setKeywordProject("");
                }}
                filterOption={false}
                showSearch
              />
              <InputTextCommon
                label="Name"
                control={suiteControl}
                name="name"
                placeholder="Type geophysic suite name here"
                isRequired={true}
              />
            </>
          )}

          <div className="flex flex-col gap-3 mt-3">
            <ButtonCommon
              type="submit"
              className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              loading={
                loadingAssignSuite ||
                loadingCreateSuite ||
                loadingUpdateSuite ||
                loadingDeleteSuite
              }
            >
              {modalState.type === ModalType.DELETE ? "Delete" : "Save"}
            </ButtonCommon>
            <ButtonCommon
              onClick={handleCancel}
              className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
            >
              Cancel
            </ButtonCommon>
          </div>
        </Form>
      </div>
    </ModalCommon>
  );
}
