"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import {
  AppstoreOutlined,
  EditOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import type { TableColumnsType } from "antd";
import { Tooltip } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { FaTrash } from "react-icons/fa6";
import { selectSuite } from "../../redux/suiteSlice";
import { getSuites } from "../../redux/suiteSlice/thunks";
import { ModalAttribute } from "./modal-suites";

const TableSuites = ({ role }: any) => {
  const suites = useAppSelector(selectSuite);
  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: suites?.result?.result?.items ?? [],
      reduxTablePagination: {
        total: suites?.result?.result?.totalCount ?? 0,
      },
      requestState: suites?.status ?? RequestState.idle,
      getDataAction: getSuites,
      filter: {
        page: 1,
        pageSize: 10,
      },
    });

  const router = useRouter();

  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });

  //table
  const columns: TableColumnsType<any> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3">
            <Tooltip placement="topLeft" title={"Edit"}>
              <EditOutlined
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "update",
                    detailInfo: record,
                  })
                }
                style={{ fontSize: 16 }}
                className="hover:text-primary cursor-pointer"
              />
            </Tooltip>
            <Tooltip placement="topLeft" title={"Delete"}>
              <FaTrash
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "delete",
                    detailInfo: record,
                  })
                }
                style={{ fontSize: 16 }}
                className="hover:text-primary cursor-pointer"
              />
            </Tooltip>
            <Tooltip placement="topLeft" title={"Add Attributes to Suite"}>
              <PlusCircleOutlined
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "assign",
                    detailInfo: record,
                  })
                }
                style={{ fontSize: 16 }}
                className="hover:text-primary cursor-pointer"
              />
            </Tooltip>
            <Tooltip placement="topLeft" title={"View Attributes"}>
              <AppstoreOutlined
                onClick={() => router.push(`/suites/${record.id}`)}
                style={{ fontSize: 16 }}
                className="hover:text-primary cursor-pointer"
              />
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Projects",
      dataIndex: "projects",
      key: "projects",
      render(value, index) {
        return (
          <div className="flex gap-2 flex-wrap">
            {value.map((item: any) => (
              <div
                key={item.id}
                className="px-2 py-1 rounded-lg"
                style={{
                  backgroundColor: item?.backgroundColour,
                  color: item?.textColour,
                }}
              >
                {item.name}
              </div>
            ))}
          </div>
        );
      },
    },
  ];

  return (
    <>
      {modalState.isOpen && (
        <ModalAttribute
          modalState={modalState}
          refresh={refresh}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold">Geophysics Suites</p>

        {/* <div className="flex justify-end">
          <button
            className={`btn btn-sm btn-primary`}
            onClick={() => {
              setModalState({
                ...modalState,
                isOpen: true,
                type: "create",
                detailInfo: null,
              });
            }}
          >
            <PlusOutlined /> Add Suites
          </button>
        </div> */}

        <hr />
        <TableCommon
          className="font-visby"
          pagination={tablePagination}
          loading={suites.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={suites?.result?.result?.items ?? []}
          footer={() => (
            <div className="justify-center my-2 ">
              <button
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: null,
                  })
                }
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  Create a geophysic suite
                </span>
              </button>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableSuites;
