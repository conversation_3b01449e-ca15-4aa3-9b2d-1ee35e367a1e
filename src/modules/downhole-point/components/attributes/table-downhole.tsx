"use client";

import { useParams } from "next/navigation";
import { useGetDownholeByProject } from "../../hooks/useGetDownholeByProject";
import { useEffect } from "react";
import { TableCommon } from "@/components/common/table-common";
import { TableColumnsType } from "antd";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
export interface ITableDownholeProps {}

export function TableDownhole(props: ITableDownholeProps) {
  const param = useParams();
  let name = param.name[0] ?? "";
  name = name.replace("%20", " ");
  const { data, request, loading } = useGetDownholeByProject();
  useEffect(() => {
    request({
      skipCount: 0,
      maxResultCount: 10,
      AttributeName: name,
    });
  }, []);
  if (!data.length) return null;

  const title = Object.keys(data[0]).filter((key) => key !== "groupId")[0];
  const columns: TableColumnsType<any> = [
    {
      title: title,
      dataIndex: title,
      key: title,
    },
    {
      title: "groupId",
      dataIndex: "groupId",
      key: "groupId",
    },
  ];
  return (
    <div className="flex flex-col gap-2">
      <p className="text-2xl font-bold uppercase">Downhole Point {name}</p>
      <TableCommon
        className="font-visby"
        loading={loading}
        columns={columns as any}
        dataSource={data}
      />
    </div>
  );
}
