"use client";
import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { UploadCommon } from "@/components/common/upload-file-common";
import { useGetDetailProject } from "@/modules/projects/hooks/useGetDetailProject";
import { selectProjects } from "@/modules/projects/redux/projectSlice";
import { getProjects } from "@/modules/projects/redux/projectSlice/thunks";
import { useGetRockGroup } from "@/modules/rock-groups/hooks/useGetRockGroups";
import { Button, Form, Table, notification } from "antd";
import { ColumnsType } from "antd/es/table";
import { useEffect, useState } from "react";
import { <PERSON>mit<PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import { toast } from "react-toastify";
import * as XLSX from "xlsx";
import downholeDataRequest from "../../api/down-hole-data.api";
import { isNumber } from "lodash";
interface FormUploadFiles {
  projectId: number;
  ExcelFile: File;
  geologySuites?: number;
  AttributeNames?: string;
}

type DownholeDataError = {
  errorAverage: Array<string>;
  errorInvalid: {
    [key: string]: Array<string>;
  };
  drillholeNotFound: Array<string>;
  attributeNotFound: Array<string>;
  errorRange: {
    [key: string]: Array<number>;
  };
  existDataErrors: {
    [key: string]: any;
  };
};

export interface DataRow {
  [key: string]: any;
}

const UploadAssay: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const projects = useAppSelector(selectProjects);
  const dispatch = useAppDispatch();
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const { handleSubmit, control, getValues, watch, setValue } =
    useForm<FormUploadFiles>({
      defaultValues: {
        projectId: globalProjectId,
        ExcelFile: undefined,
      },
    });
  const [fileXlsx, setFileXlsx] = useState<File>();
  const [isValidateState, setIsValidateState] = useState<RequestState>(
    RequestState.idle
  );
  const [validateData, setValidateData] = useState<DownholeDataError>();

  const [data, setData] = useState<DataRow[]>([]);
  const [columns, setColumns] = useState<ColumnsType<DataRow>>([]);

  const readExcelFile = async (file: File, projectId: string) => {
    if (!file) return;
    setFileXlsx(file);
    if (!projectId) return;
    setIsValidateState(RequestState.pending);

    const reader = new FileReader();
    reader.onload = async (e: ProgressEvent<FileReader>) => {
      const binaryStr = e.target?.result;
      if (typeof binaryStr !== "string") return;

      const workbook = XLSX.read(binaryStr, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(sheet, {
        header: 1,
      }) as any[][];

      const headers = jsonData[0] as string[];
      const rows = jsonData.slice(1);

      setColumns(
        headers.map((columnName, index) => ({
          title: () => {
            return <div>{columnName}</div>;
          },
          dataIndex: String(index),
          key: String(index),
          render: (text: string) => {
            // Kiểm tra nếu text là số và làm tròn đến 2 chữ số thập phân
            if (isNumber(text)) {
              const numberValue = parseFloat(text);
              const roundedValue = numberValue.toFixed(2);
              // Chuyển đổi số đã được làm tròn thành số thực để loại bỏ các số không cần thiết ở cuối
              return parseFloat(roundedValue).toString();
            }

            return text;
          },
        }))
      );
      setData(
        rows.map((row, rowIndex) => {
          const rowData: DataRow = { key: rowIndex };
          row.forEach((cell, i) => {
            rowData[String(i)] = cell;
          });
          return rowData;
        })
      );
    };
    reader.readAsBinaryString(file);

    setIsValidateState(RequestState.error);
  };

  const onSubmit: SubmitHandler<FormUploadFiles> = async (data) => {
    if (!fileXlsx) return;

    setIsUploading(true);
    const response = await downholeDataRequest.uploadAssayData({
      ExcelFile: fileXlsx,
      projectId: data.projectId as any,
    });

    if (response.state === RequestState.error) {
      notification.error({ message: response.message });
    }
    if (response.state === RequestState.success) {
      toast.success("Upload down hole data successfully!");
    }
    setIsUploading(false);
  };

  useEffect(() => {
    dispatch(
      getProjects({
        page: 1,
        pageSize: 30,
      })
    );
  }, []);

  const _existDataErrors = validateData?.existDataErrors ?? {};
  const existDataErrors = Object.keys(_existDataErrors).map((drillHole) => ({
    drillHole: drillHole,
    existedData: Object.keys(_existDataErrors[drillHole]),
  }));
  const [isOpenModal, setIsOpenModal] = useState(false);
  const handleCancel = () => {
    setIsOpenModal(false);
  };
  const selectedProjectId = watch("projectId");
  const { data: detailProject, request: requestGetDetailProject } =
    useGetDetailProject();
  useEffect(() => {
    if (selectedProjectId) {
      requestGetDetailProject(selectedProjectId as any);
    }
  }, [selectedProjectId]);
  const geologySuites = watch("geologySuites");
  const { request: requestRockGroup } = useGetRockGroup();
  useEffect(() => {
    if (geologySuites) {
      requestRockGroup(geologySuites as any, (data) => {
        setValue("AttributeNames", data.geologyAttribute.name);
      });
    }
  }, [geologySuites]);

  return (
    <div className="flex flex-col gap-5">
      <ModalCommon
        open={isOpenModal}
        centered
        padding={0}
        footer={null}
        onCancel={handleCancel}
        style={{ borderRadius: 8 }}
        width={450}
        closable={false}
      >
        Do you want to upload this file?
        <div className="flex">
          <Button>Yes</Button>
        </div>
      </ModalCommon>
      <p className="text-34-34 font-semibold">Load Assay Data</p>
      <hr />
      <div className="grid grid-cols-12 gap-5">
        <Form
          onFinish={handleSubmit(onSubmit)}
          className="col-span-6 flex flex-col gap-3"
        >
          <div className="flex gap-2 flex-row">
            <div className="w-[50%] flex flex-col gap-2">
              <SelectCommon
                name="projectId"
                options={(projects?.result?.items ?? []).map((d) => ({
                  label: d.name,
                  value: d.id,
                }))}
                loading={projects.status === RequestState.pending}
                control={control}
                label="Project"
                placeholder="Select a project"
                onChange={async (projectId) => {
                  setValue("geologySuites", undefined);
                  setValue("AttributeNames", undefined);
                  return fileXlsx && readExcelFile(fileXlsx, projectId);
                }}
                allowClear
              />
            </div>
            <div className="w-[50%]">
              <UploadCommon
                name="ExcelFile"
                control={control}
                label="File"
                placeholder="Select file"
                file={fileXlsx}
                onUploadAsync={async (file) => {
                  readExcelFile(file, getValues("projectId")?.toString());
                }}
              />
            </div>
          </div>
          <div className="flex justify-center">
            <ButtonCommon
              loading={isUploading || isValidateState === RequestState.pending}
              // disabled={!fileXlsx || isValidateState === RequestState.error}
              type="submit"
              className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isValidateState === RequestState.pending
                ? "Validating data..."
                : "Upload"}
            </ButtonCommon>
          </div>
        </Form>
      </div>
      {fileXlsx && getValues("projectId") && (
        <div>
          <Table
            loading={isValidateState === RequestState.pending}
            columns={columns}
            dataSource={data}
            title={() => {
              return (
                <div className="flex justify-between">
                  <div>
                    <p className="font-medium">{`File: ${fileXlsx?.name}`}</p>
                    <div>
                      {typeof validateData === "string" && (
                        <div className="font-bold text-red-600">
                          {validateData}
                        </div>
                      )}
                      {(validateData?.errorAverage ?? []).filter(Boolean)
                        .length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            {(validateData?.errorAverage ?? []).join(", ")}:
                          </span>
                          data interval less than minimum interval
                        </div>
                      )}
                      {(
                        Object.keys(validateData?.errorRange ?? {}) ?? []
                      ).filter(Boolean).length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            {(Object.keys(validateData?.errorRange ?? {}) ?? [])
                              .map((error) => String(error))
                              .join(", ")}
                            :
                          </span>{" "}
                          out of range
                        </div>
                      )}
                      {(
                        Object.keys(validateData?.errorInvalid ?? {}) ?? []
                      ).filter(Boolean).length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            {" "}
                            {(
                              Object.keys(validateData?.errorInvalid ?? {}) ??
                              []
                            )
                              .map((error) => String(error))
                              .join(", ")}
                            :
                          </span>{" "}
                          has some invalid data
                        </div>
                      )}
                      {(validateData?.attributeNotFound ?? []).filter(Boolean)
                        .length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            Attribute{" "}
                            {(validateData?.attributeNotFound ?? []).join(", ")}
                            :{" "}
                          </span>
                          not found
                        </div>
                      )}
                      {(validateData?.drillholeNotFound ?? []).filter(Boolean)
                        .length > 0 && (
                        <div>
                          <span className="font-bold text-orange-500">
                            Warning:
                          </span>{" "}
                          drillhole{" "}
                          {(validateData?.drillholeNotFound ?? []).join(", ")}{" "}
                          not found
                        </div>
                      )}
                      {validateData?.existDataErrors.length > 0 &&
                        existDataErrors.filter(Boolean).length && (
                          <>
                            {existDataErrors.map((data) => {
                              return (
                                <div>
                                  <span className="font-bold text-orange-500">
                                    Warning:
                                  </span>{" "}
                                  <span className="font-bold text-red-600">
                                    {data.drillHole}
                                  </span>
                                  <span> has existing data in the table: </span>
                                  <span className="font-bold text-red-600">
                                    {data.existedData.join(", ")}
                                  </span>
                                </div>
                              );
                            })}
                          </>
                        )}
                    </div>
                  </div>
                  <p className="font-medium">Total rows: {data.length}</p>
                </div>
              );
            }}
          />
        </div>
      )}
    </div>
  );
};
export default UploadAssay;
