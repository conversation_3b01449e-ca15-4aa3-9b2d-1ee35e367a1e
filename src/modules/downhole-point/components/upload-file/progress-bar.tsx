import React from "react";
import { Flex, Progress } from "antd";

const ProgressBar = ({
  completed,
  errors,
  total,
}: {
  completed: number;
  errors: number;
  total: number;
}) => {
  const completedPercent = total
    ? parseFloat(((completed / total) * 100).toFixed(2))
    : 0;
  const errorPercent = total
    ? parseFloat(((errors / total) * 100).toFixed(2))
    : 0;
  const uploadingPercent = total
    ? parseFloat((((total - completed - errors) / total) * 100).toFixed(2))
    : 0;
  return (
    <Flex gap="small" vertical>
      <div className="flex gap-3 items-center">
        <p className="font-medium text-sm w-[150px]">Completed</p>
        <Progress
          percent={completedPercent}
          size={[300, 20]}
          format={(percent) => (
            <p className="text-green-500">
              {percent}% ({completed})
            </p>
          )}
        />
      </div>
      {/* <div className="flex gap-3 items-center">
        <p className="font-medium text-sm w-[150px]">Errors</p>
        <Progress
          percent={errorPercent}
          size={[300, 20]}
          strokeColor="#FF4D4F" // Red color for errors
          format={(percent) => (
            <p className="text-red-500">
              {percent}% ({errors})
            </p>
          )}
        />
      </div> */}
      <div className="flex gap-3 items-center">
        <p className="font-medium text-sm w-[150px]">Ready to load</p>
        <Progress
          percent={uploadingPercent}
          size={[300, 20]}
          format={(percent) => (
            <p className="text-yellow-500">
              {percent}% ({total - errors - completed})
            </p>
          )}
        />
      </div>
    </Flex>
  );
};

export default ProgressBar;
