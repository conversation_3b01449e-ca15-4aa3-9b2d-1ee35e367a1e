"use client";
import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { UploadCommon } from "@/components/common/upload-file-common";
import { useGetDetailProject } from "@/modules/projects/hooks/useGetDetailProject";
import { selectProjects } from "@/modules/projects/redux/projectSlice";
import { getProjects } from "@/modules/projects/redux/projectSlice/thunks";
import { useGetRockGroup } from "@/modules/rock-groups/hooks/useGetRockGroups";
import { Button, Form, Table, notification } from "antd";
import { ColumnsType } from "antd/es/table";
import { useEffect, useState } from "react";
import { <PERSON>mit<PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import * as XLSX from "xlsx";
import downholeDataRequest from "../../api/down-hole-data.api";
import { isNumber } from "lodash";
interface FormUploadFiles {
  projectId: number;
  ExcelFile: File;
  geologySuites?: number;
  AttributeNames?: number[];
}

type DownholeDataError = {
  errorAverage: Array<string>;
  errorInvalid: {
    [key: string]: Array<string>;
  };
  drillholeNotFound: Array<string>;
  attributeNotFound: Array<string>;
  errorRange: {
    [key: string]: Array<number>;
  };
  existDataErrors: {
    [key: string]: any;
  };
};

export interface DataRow {
  [key: string]: any;
}

const UploadDownholePoint: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const projects = useAppSelector(selectProjects);
  const dispatch = useAppDispatch();
  const { handleSubmit, control, getValues, watch } = useForm<FormUploadFiles>({
    defaultValues: {
      projectId: undefined,
      ExcelFile: undefined,
    },
  });
  const [fileXlsx, setFileXlsx] = useState<File>();
  const [isValidateState, setIsValidateState] = useState<RequestState>(
    RequestState.idle
  );
  const [validateData, setValidateData] = useState<DownholeDataError>();

  const [data, setData] = useState<DataRow[]>([]);
  const [columns, setColumns] = useState<ColumnsType<DataRow>>([]);

  const checkPointDataError = (
    errors: DownholeDataError,
    columnName: string,
    value: number | string
  ) => {
    const considerValue = parseFloat(parseFloat(String(value)).toFixed(9));
    if ((errors?.errorAverage ?? []).includes(columnName)) return true;
    if ((errors?.errorInvalid ?? {})?.[columnName]?.includes(value.toString()))
      return true;
    if ((errors?.errorRange ?? {})?.[columnName]?.includes(considerValue))
      return true;
    return false;
  };

  const checkColumnError = (errors: DownholeDataError, columnName: string) => {
    if ((errors?.errorAverage ?? []).includes(columnName)) return true;
    if (Object.keys(errors?.errorInvalid ?? {}).includes(columnName))
      return true;
    if (Object.keys(errors?.errorRange ?? {}).includes(columnName)) return true;
    return false;
  };

  const readExcelFile = async (file: File, projectId: string) => {
    if (!file) return;
    setFileXlsx(file);
    if (!projectId) return;
    setIsValidateState(RequestState.pending);

    const validateResponse = await downholeDataRequest.validate({
      projectId: projectId,
      file: file,
    });

    setValidateData(validateResponse.result);

    const reader = new FileReader();
    reader.onload = async (e: ProgressEvent<FileReader>) => {
      const binaryStr = e.target?.result;
      if (typeof binaryStr !== "string") return;

      const workbook = XLSX.read(binaryStr, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(sheet, {
        header: 1,
      }) as any[][];

      const headers = jsonData[0] as string[];
      const rows = jsonData.slice(1);

      setColumns(
        headers.map((columnName, index) => ({
          title: () => {
            if (checkColumnError(validateResponse.result, columnName))
              return (
                <span className="text-red-700 font-bold">{columnName}</span>
              );
            return <div>{columnName}</div>;
          },
          dataIndex: String(index),
          key: String(index),
          render: (text: string) => {
            console.log("text", text);

            if (
              checkPointDataError(validateResponse.result, columnName, text)
            ) {
              return <span className="text-red-700 font-bold">{text}</span>;
            }

            // Kiểm tra nếu text là số và làm tròn đến 2 chữ số thập phân
            if (isNumber(text)) {
              const numberValue = parseFloat(text);
              const roundedValue = numberValue.toFixed(2);
              // Chuyển đổi số đã được làm tròn thành số thực để loại bỏ các số không cần thiết ở cuối
              return parseFloat(roundedValue).toString();
            }

            return text;
          },
        }))
      );
      setData(
        rows.map((row, rowIndex) => {
          const rowData: DataRow = { key: rowIndex };
          row.forEach((cell, i) => {
            rowData[String(i)] = cell;
          });
          return rowData;
        })
      );
    };
    reader.readAsBinaryString(file);
    if (validateResponse.state === RequestState.success) {
      return setIsValidateState(RequestState.success);
    }

    // If error but drillhole not found, we can still upload
    if (
      (validateResponse?.result?.drillholeNotFound ?? []).filter(Boolean)
        .length > 0 &&
      (validateResponse?.result?.attributeNotFound ?? []).filter(Boolean)
        .length === 0 &&
      (validateResponse?.result?.errorAverage ?? []).filter(Boolean).length ===
        0 &&
      (validateResponse?.result?.existDataErrors ?? []).filter(Boolean)
        .length === 0 &&
      (Object.keys(validateResponse?.result?.errorRange ?? {}) ?? []).filter(
        Boolean
      ).length === 0 &&
      (Object.keys(validateResponse?.result?.errorInvalid ?? {}) ?? []).filter(
        Boolean
      ).length === 0
    ) {
      return setIsValidateState(RequestState.success);
    }

    setIsValidateState(RequestState.error);
  };

  const onSubmit: SubmitHandler<FormUploadFiles> = async (data) => {
    if (!fileXlsx) return;

    setIsUploading(true);
    const response = await downholeDataRequest.upload({
      projectId: data.projectId.toString(),
      file: fileXlsx,
    });

    if (response.state === RequestState.error) {
      notification.error({ message: response.message });
    }
    if (response.state === RequestState.success) {
      notification.success({ message: "Upload down hole data successfully!" });
    }
    setIsUploading(false);
  };

  useEffect(() => {
    dispatch(
      getProjects({
        page: 1,
        pageSize: 30,
      })
    );
  }, []);

  const _existDataErrors = validateData?.existDataErrors ?? {};
  const existDataErrors = Object.keys(_existDataErrors).map((drillHole) => ({
    drillHole: drillHole,
    existedData: Object.keys(_existDataErrors[drillHole]),
  }));
  const [isOpenModal, setIsOpenModal] = useState(false);
  const handleCancel = () => {
    setIsOpenModal(false);
  };
  const selectedProjectId = watch("projectId");
  const { data: detailProject, request: requestGetDetailProject } =
    useGetDetailProject();
  useEffect(() => {
    if (selectedProjectId) {
      requestGetDetailProject(selectedProjectId as any);
    }
  }, [selectedProjectId]);
  const rockGroups = watch("geologySuites");
  const { data: rockGroupsData, request: requestRockGroups } =
    useGetRockGroup();
  useEffect(() => {
    if (rockGroups) {
      requestRockGroups(rockGroups as any);
    }
  }, [rockGroups]);
  console.log("data", data);

  return (
    <div className="flex flex-col gap-5">
      <ModalCommon
        open={isOpenModal}
        centered
        padding={0}
        footer={null}
        onCancel={handleCancel}
        style={{ borderRadius: 8 }}
        width={450}
        closable={false}
      >
        Do you want to upload this file?
        <div className="flex">
          <Button>Yes</Button>
        </div>
      </ModalCommon>
      <p className="text-34-34 font-semibold">Load Downhole Point Data</p>
      <hr />
      <div className="grid grid-cols-12 gap-5">
        <Form
          onFinish={handleSubmit(onSubmit)}
          className="col-span-6 flex flex-col gap-3"
        >
          <div className="flex gap-2 flex-row">
            <div className="w-[50%]">
              <SelectCommon
                name="projectId"
                options={(projects?.result?.items ?? []).map((d) => ({
                  label: d.name,
                  value: d.id,
                }))}
                loading={projects.status === RequestState.pending}
                control={control}
                label="Project"
                placeholder="Select a project"
                onChange={async (projectId) =>
                  fileXlsx && readExcelFile(fileXlsx, projectId)
                }
                allowClear
              />
            </div>
            <div className="w-[50%]">
              <UploadCommon
                name="ExcelFile"
                control={control}
                label="File"
                placeholder="Select file"
                file={fileXlsx}
                onUploadAsync={async (file) => {
                  console.log("file", file);

                  readExcelFile(file, getValues("projectId")?.toString());
                }}
              />
            </div>
          </div>
          <div className="flex justify-center">
            <ButtonCommon
              loading={isUploading || isValidateState === RequestState.pending}
              disabled={!fileXlsx || isValidateState === RequestState.error}
              type="submit"
              className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isValidateState === RequestState.pending
                ? "Validating data..."
                : "Upload"}
            </ButtonCommon>
          </div>
        </Form>
      </div>
      {fileXlsx && getValues("projectId") && (
        <div>
          <Table
            loading={isValidateState === RequestState.pending}
            columns={columns}
            dataSource={data}
            title={() => {
              return (
                <div className="flex justify-between">
                  <div>
                    <p className="font-medium">{`File: ${fileXlsx?.name}`}</p>
                    <div>
                      {typeof validateData === "string" && (
                        <div className="font-bold text-red-600">
                          {validateData}
                        </div>
                      )}
                      {(validateData?.errorAverage ?? []).filter(Boolean)
                        .length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            {(validateData?.errorAverage ?? []).join(", ")}:
                          </span>
                          data interval less than minimum interval
                        </div>
                      )}
                      {(
                        Object.keys(validateData?.errorRange ?? {}) ?? []
                      ).filter(Boolean).length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            {(Object.keys(validateData?.errorRange ?? {}) ?? [])
                              .map((error) => String(error))
                              .join(", ")}
                            :
                          </span>{" "}
                          out of range
                        </div>
                      )}
                      {(
                        Object.keys(validateData?.errorInvalid ?? {}) ?? []
                      ).filter(Boolean).length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            {" "}
                            {(
                              Object.keys(validateData?.errorInvalid ?? {}) ??
                              []
                            )
                              .map((error) => String(error))
                              .join(", ")}
                            :
                          </span>{" "}
                          has some invalid data
                        </div>
                      )}
                      {(validateData?.attributeNotFound ?? []).filter(Boolean)
                        .length > 0 && (
                        <div>
                          <span className="font-bold text-red-600">
                            Attribute{" "}
                            {(validateData?.attributeNotFound ?? []).join(", ")}
                            :{" "}
                          </span>
                          not found
                        </div>
                      )}
                      {(validateData?.drillholeNotFound ?? []).filter(Boolean)
                        .length > 0 && (
                        <div>
                          <span className="font-bold text-orange-500">
                            Warning:
                          </span>{" "}
                          drillhole{" "}
                          {(validateData?.drillholeNotFound ?? []).join(", ")}{" "}
                          not found
                        </div>
                      )}
                      {validateData?.existDataErrors?.length > 0 &&
                        existDataErrors.filter(Boolean).length && (
                          <>
                            {existDataErrors.map((data) => {
                              return (
                                <div>
                                  <span className="font-bold text-orange-500">
                                    Warning:
                                  </span>{" "}
                                  <span className="font-bold text-red-600">
                                    {data.drillHole}
                                  </span>
                                  <span> has existing data in the table: </span>
                                  <span className="font-bold text-red-600">
                                    {data.existedData.join(", ")}
                                  </span>
                                </div>
                              );
                            })}
                          </>
                        )}
                    </div>
                  </div>
                  <p className="font-medium">Total rows: {data.length}</p>
                </div>
              );
            }}
          />
        </div>
      )}
    </div>
  );
};
export default UploadDownholePoint;
