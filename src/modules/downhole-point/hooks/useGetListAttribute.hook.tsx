import { useState } from "react";
import attributeRequest from "../api/attributes.api";

export const useGetListAttributes = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: {
      keyword?: string;
      skipCount: number;
      maxResultCount: number;
      isActive?: boolean;
    },
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await attributeRequest.getList({
      ...params,
      isActive: params?.isActive ?? true,
    });
    if (response?.status === 200) {
      setData(
        response.data && response.data.result && response.data.result.items
          ? response.data.result.items
          : []
      );
      setTotal(
        response.data && response.data.result
          ? response.data.result?.totalCount
          : 0
      );
      onSuccess && onSuccess(response.data);
      setLoading(false);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
