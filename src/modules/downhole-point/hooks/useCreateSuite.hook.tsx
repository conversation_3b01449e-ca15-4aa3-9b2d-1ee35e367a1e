import { useState } from "react";
import suiteRequest from "../api/suite.api";

export const useCreateSuite = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: any,
    onSuccess?: Function,
    onError?: Function
  ) {
    try {
      setLoading(true);
      const response = await suiteRequest.create(params);
      if (response.state === "success") {
        onSuccess && onSuccess(response.data);
      } else {
      }
      setLoading(false);
    } catch (error) {
      onError && onError(error);
      setLoading(false);
    }
  }
  return { request, loading };
};
