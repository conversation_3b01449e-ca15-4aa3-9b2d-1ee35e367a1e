import { useState } from "react";
import attributeRequest from "../api/attributes.api";

export const useDeleteAttribute = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function
  ) {
    try {
      setLoading(true);
      const response = await attributeRequest.delete(params);

      if (response.status === 200) {
        onSuccess && onSuccess(response.data);
      } else {
      }
      setLoading(false);
    } catch (error) {
      onError && onError(error);
      setLoading(false);
    }
  }
  return { request, loading };
};
