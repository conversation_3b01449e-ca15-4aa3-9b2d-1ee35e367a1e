import { useState } from "react";
import suiteRequest from "../api/suite.api";

export const useGetSuiteByLoggingView = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: {
      loggingViewId: number;
      skipCount: number;
      maxResultCount: number;
    },
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await suiteRequest.getSuiteByLoggingViewId(params);
    if (response?.state === "success") {
      setData(response.data);
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data };
};
