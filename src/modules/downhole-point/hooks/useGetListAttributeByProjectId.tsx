import { useState } from "react";
import attributeRequest from "../api/attributes.api";
import { RequestState } from "@/common/configs/app.contants";

export const useGetListAttributeByProjectId = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: {
      id: string;
      drillHoleNames: string[];
    },
    onSuccess?: Function
  ) => {
    setLoading(true);
    let attributes: any[] = [];
    for (let i = 0; i < params?.drillHoleNames.length; i++) {
      const response = await attributeRequest.getAttributeByProjectId({
        id: params.id,
        drillHole: params.drillHoleNames[i],
      });
      if (response.state === RequestState.success) {
        attributes = [...attributes, ...response.data];
      }
    }

    const uniqueAttributes = attributes.filter(
      (obj, index, self) => index === self.findIndex((o) => o.id === obj.id)
    );

    setData(uniqueAttributes);
    onSuccess && onSuccess(uniqueAttributes);
    setLoading(false);
    return uniqueAttributes;
  };

  return { request, loading, data };
};
