import { useState } from "react";
import downholeDataRequest from "../api/down-hole-data.api";
import { RequestState } from "@/common/configs/app.contants";

export const useGetGeologyData = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: {
      AttributeName?: any;
      projectId?: any;
      DrillHoleName?: any[];
      skipCount?: number;
      maxResultCount?: number;
      DepthFrom?: number;
      DepthTo?: number;
      GeologySuiteId?: any;
    },
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await downholeDataRequest.getGeologyData(params);

    if (response?.state === RequestState.success) {
      setData(response?.data?.items ?? []);
      setTotal(response?.data?.totalCount ?? 0);
      onSuccess && onSuccess(response?.data?.items);
      setLoading(false);
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
