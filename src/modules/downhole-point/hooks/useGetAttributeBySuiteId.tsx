import { useState } from "react";
import attributeRequest from "../api/attributes.api";

export const useGetListAttributeBySuiteId = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: {
      Id: string;
    },
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await attributeRequest.getAttributeBySuiteId(params);
    if (response?.state === "success") {
      setData(response.data);
      onSuccess && onSuccess(response.data);
      setLoading(false);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data };
};
