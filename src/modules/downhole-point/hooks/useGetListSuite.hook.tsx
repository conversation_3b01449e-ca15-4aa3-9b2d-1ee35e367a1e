import { useState } from "react";
import suiteRequest from "../api/suite.api";

export const useGetListSuite = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>();
  const request = async (
    params: {
      keyword?: string;
      skipCount: number;
      maxResultCount: number;
    },
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await suiteRequest.getList(params);
    setData(response?.data?.result?.items);

    if (response?.status === 200) {
      onSuccess && onSuccess(response.data);
      setLoading(false);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data };
};
