import { useState } from "react";
import attributeRequest from "../api/attributes.api";

export const useAssignSuite = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      attributeIds: any[];
      suiteId: string;
    },
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await attributeRequest.assign(params);
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
    } else {
      onError && onError(response.message);
    }
    setLoading(false);
  }
  return { request, loading };
};
