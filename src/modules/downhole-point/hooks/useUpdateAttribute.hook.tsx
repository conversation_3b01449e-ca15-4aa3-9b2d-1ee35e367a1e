import { useState } from "react";
import attributeRequest from "../api/attributes.api";
import { AttributeBodyType } from "../schemaValidator/attribute.schema";

export const useUpdateAttribute = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: AttributeBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    try {
      setLoading(true);
      const response = await attributeRequest.update(params);

      if (response.status === 200) {
        onSuccess && onSuccess(response.data);
      } else {
      }
      setLoading(false);
    } catch (error) {
      onError && onError(error);
      setLoading(false);
    }
  }
  return { request, loading };
};
