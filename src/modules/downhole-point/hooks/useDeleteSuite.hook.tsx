import { useState } from "react";
import suiteRequest from "../api/suite.api";

export const useDeleteSuite = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function
  ) {
    try {
      setLoading(true);
      const response = await suiteRequest.delete(params);
      if (response.status === 200) {
        onSuccess && onSuccess(response.data);
      } else {
      }
      setLoading(false);
    } catch (error) {
      onError && onError(error);
      setLoading(false);
    }
  }
  return { request, loading };
};
