import { useState } from "react";
import attributeRequest from "../api/attributes.api";
import { AttributeBodyType } from "../schemaValidator/attribute.schema";

export const useCreateAttribute = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: AttributeBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await attributeRequest.create(params);
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
    } else {
      onError && onError(response.message);
    }
    setLoading(false);
  }
  return { request, loading };
};
