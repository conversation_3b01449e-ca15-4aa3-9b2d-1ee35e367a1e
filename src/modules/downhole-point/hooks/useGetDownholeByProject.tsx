import { useState } from "react";
import downholeDataRequest from "../api/down-hole-data.api";
import { RequestState } from "@/common/configs/app.contants";

export const useGetDownholeByProject = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: {
      projectId?: any;
      SuiteId?: File;
      AttributeName?: String;
      skipCount?: number;
      maxResultCount?: number;
      DrillHoleName?: string | string[];
      DepthFrom?: number;
      DepthTo?: number;
      GeophysicsSuitId?: any;
    },
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await downholeDataRequest.getData(params);

    if (response?.state === RequestState.success) {
      setData(response?.data?.items ?? []);
      setTotal(response?.data?.totalCount ?? 0);
      onSuccess && onSuccess(response?.data?.items);
      setLoading(false);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
