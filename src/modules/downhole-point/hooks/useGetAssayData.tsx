import { useState } from "react";
import downholeDataRequest from "../api/down-hole-data.api";

export const useGetAssayData = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: {
      AttributeName?: any;
      projectId?: any;
      DrillHoleName?: any[];
      skipCount?: number;
      maxResultCount?: number;
      AssaySuitId?: any;
    },
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await downholeDataRequest.getAssayData(params);
    if (response?.state === "success") {
      setData(response.data?.items ?? []);
      onSuccess && onSuccess(response.data?.items);
      setLoading(false);
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data };
};
