import { RequestState } from "@/common/configs/app.contants";
import { IPaginationResult } from "@/common/interfaces/response/IPaginationResult";
import { createSlice } from "@reduxjs/toolkit";
import { getAttributes } from "./thunks";

const initialState: AccountSettingsSliceState = {
  status: RequestState.idle,
};

export const attributeSlice = createSlice({
  name: "attributes",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getAttributes.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getAttributes.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      });
  },
});

export interface AccountSettingsSliceState {
  result?: any;
  status: RequestState;
}

export const {} = attributeSlice.actions;
