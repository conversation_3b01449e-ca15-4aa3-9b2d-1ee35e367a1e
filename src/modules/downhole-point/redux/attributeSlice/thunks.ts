import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import { AttributeQuery } from "../../interface/attribute.query";
import attributeRequest from "../../api/attributes.api";

export const getAttributes = createAppAsyncThunk(
  "attribute/list",
  async (query: AttributeQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await attributeRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });
    return response;
  }
);
