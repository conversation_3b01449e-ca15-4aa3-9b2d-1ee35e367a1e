"use client";
import useEffectAfterMounted from "@/common/hooks/useSkipFirstRender";
import { SortOrder } from "@/common/interfaces/general/general.types";
import { InputTextCommon } from "@/components/common/input-text";
import { SelectCommon } from "@/components/common/select-common";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import { drillHoleStatusOptionsExport } from "@/modules/drillhole/model/enum/drillhole.enum";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import {
  categoryOptionExport,
  imageSizeOptions,
} from "@/modules/image/constants/image.constant";
import { transformToHierarchicalNodes } from "@/modules/image/helpers/image.helpers";
import { useGetListProject } from "@/modules/projects/hooks";
import { useGetListProspect } from "@/modules/prospect/hooks/useGetListProspect";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON>, Form, Spin, TreeSelect } from "antd";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { MdOutlineArrowBackIosNew } from "react-icons/md";
import { toast } from "react-toastify";
import { DataOutputTypes, dataSourceTypes } from "../constants/options";
import { useCreateExportTemplate } from "../hooks/useCreateExportTemplate";
import { useGetDetailExportTemplate } from "../hooks/useGetDetailExportTemplate";
import { useUpdateExportTemplate } from "../hooks/useUpdateExportTemplate";
import {
  exportTemplateBody,
  ExportTemplateBodyType,
} from "../model/schema/export-template.schema";

export interface IExportTemplateProps {
  id?: string;
}
export function ExportTemplate({ id }: IExportTemplateProps) {
  const router = useRouter();
  const { control, handleSubmit, watch, setValue, reset } =
    useForm<ExportTemplateBodyType>({
      resolver: zodResolver(exportTemplateBody),
      defaultValues: {
        dataOutput: DataOutputTypes[0].value,
        imageSize: [imageSizeOptions[0].value],
        dataSource: categoryOptionExport[0].value,
        drillHoleStatus: drillHoleStatusOptionsExport[0].value,
        updateStatus: drillHoleStatusOptionsExport[0].value,
      },
    });

  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const [maxResultCountDrillhole, setMaxResultCountDrillhole] = useState(10);
  const [maxResultCountProspect, setMaxResultCountProspect] = useState(10);
  const [keywordProject, setKeywordProject] = useState("");
  const [keywordProspect, setKeywordProspect] = useState("");
  const [keywordDrillhole, setKeywordDrillhole] = useState("");
  const { request: getDetailTemplate } = useGetDetailExportTemplate();

  const dataSource = watch("dataSource");

  const {
    loading: loadingUpdateExportTemplate,
    request: requestUpdateExportTemplate,
  } = useUpdateExportTemplate();
  const {
    request: requestCreateExportTemplate,
    loading: loadingCreateExportTemplate,
  } = useCreateExportTemplate();
  const {
    request: requestGetListProject,
    data: projects,
    loading: loadingListProject,
  } = useGetListProject();
  const {
    request: requestGetListProspect,
    data: prospects,
    loading: loadingListProspect,
  } = useGetListProspect();
  const {
    request: requestGetListDrillHole,
    data: drillholes,
    loading: loadingListDrillhole,
  } = useGetListDrillhole();
  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  const handleScrollProspect = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProspect(maxResultCountProspect + 10);
    }
  };
  const handleScrollDrillhole = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountDrillhole(maxResultCountDrillhole + 10);
    }
  };
  const selectedProjectId = watch("projectId");
  const selectedProspectId = watch("prospectId");

  useEffectAfterMounted(() => {
    requestGetListProject({
      maxResultCount: maxResultCountProject,
      skipCount: 0,
      sortField: "name",
      sortOrder: SortOrder.ASC,
      keyword: keywordProject,
    });
  }, [maxResultCountProject, keywordProject]);

  useEffectAfterMounted(() => {
    if (selectedProjectId) {
      requestGetListProspect({
        maxResultCount: maxResultCountProject,
        skipCount: 0,
        projectIds: selectedProjectId ? [selectedProjectId] : undefined,
        keyword: keywordProspect,
      });
    }
  }, [selectedProjectId, keywordProspect, maxResultCountProspect]);

  useEffectAfterMounted(() => {
    if (selectedProspectId) {
      requestGetListDrillHole({
        maxResultCount: maxResultCountDrillhole,
        skipCount: 0,
        projectIds: selectedProjectId ? [selectedProjectId] : undefined,
        prospectIds: selectedProspectId ? [selectedProspectId] : undefined,
        keyword: keywordDrillhole,
      });
    }
  }, [selectedProspectId, keywordDrillhole, maxResultCountDrillhole]);

  const onSubmit = (data: ExportTemplateBodyType) => {
    if (id) {
      requestUpdateExportTemplate(
        {
          ...data,
          id,
        },
        () => {
          router.push("/export-template");
          toast.success("Update export template success");
        }
      );
    } else {
      requestCreateExportTemplate(data, () => {
        router.push("/export-template");
        toast.success("Create export template success");
      });
    }
  };
  const { data: imageTypeData, isLoading: isFetchingImageType } =
    useQueryImageType(); // Assuming isLoading or isFetching is returned
  const allImageTypes = imageTypeData?.data?.items;
  const transformDataToTreeNodes = (data: any[] | undefined) => {
    if (!data) return [];
    return data.map((type: any) => ({
      title: type.name,
      value: `type-${type.id}`,
      key: `type-${type.id}`,
      children: (type.imageSubtypes || []).map((subtype: any) => ({
        title: subtype.name,
        value: `subtype-${subtype.id}-type-${type.id}`,
        key: `subtype-${subtype.id}-type-${type.id}`,
      })),
    }));
  };
  const imageTypeTreeData = useMemo(
    () => transformDataToTreeNodes(allImageTypes),
    [imageTypeData]
  );

  useEffect(() => {
    (async () => {
      if (id) {
        const detailTemplate = await getDetailTemplate({
          Id: id,
        });
        if (detailTemplate) {
          const imageTypeIds = [
            ...new Set(
              detailTemplate?.imageSubtypes.map((item: any) => item.imageTypeId)
            ),
          ] as number[];
          const imageSubTypeIds = detailTemplate?.imageSubtypes.map(
            (item: any) => item.id
          );
          const imageSubtypes = detailTemplate?.imageSubtypes.map(
            (item: any) => {
              return {
                imageTypeId: item.imageTypeId,
                imageSubtypeId: item.id,
              };
            }
          );
          //set value selectedNodeValues
          const _selectedNodeValues = imageSubtypes.map((item: any) => {
            return `subtype-${item.imageSubtypeId}-type-${item.imageTypeId}`;
          });
          setSelectedNodeValues(_selectedNodeValues);

          reset({
            ...detailTemplate,
            imageTypeIds,
            imageSubTypeIds,
          });
        }
      }
    })();
  }, [id]);
  const [selectedNodeValues, setSelectedNodeValues] = useState<string[]>([]);
  const [selectedHierarchicalNodes, setSelectedHierarchicalNodes] =
    useState<any>();
  const treeSelectValue = useMemo(() => {
    return selectedNodeValues || [];
  }, [selectedNodeValues]);

  const handleImageTypeTreeSelectChange = (selectedValues: string[]) => {
    setSelectedNodeValues(selectedValues);
    const hierarchicalNodes = transformToHierarchicalNodes(
      selectedValues,
      imageTypeTreeData
    );
    setSelectedHierarchicalNodes(hierarchicalNodes);
  };

  useEffect(() => {
    // Parse all selected values to extract type and subtype IDs
    const selectedTypeIds = new Set<number>();
    const selectedSubTypeIds = new Set<number>();

    selectedNodeValues.forEach((value) => {
      if (value.includes("subtype")) {
        // Format: "subtype-{subtypeId}-type-{typeId}"
        const parts = value.split("-");
        const subTypeId = Number(parts[1]);
        const typeId = Number(parts[3]);

        // Add both the parent type and the specific subtype
        selectedTypeIds.add(typeId);
        selectedSubTypeIds.add(subTypeId);
      } else if (value.includes("type") && !value.includes("subtype")) {
        // Format: "type-{typeId}"
        const typeId = Number(value.split("-")[1]);

        // Add the type and all its subtypes
        selectedTypeIds.add(typeId);
        const imageType = allImageTypes?.find((type) => type.id === typeId);
        if (imageType?.imageSubtypes) {
          imageType.imageSubtypes.forEach((subtype) => {
            selectedSubTypeIds.add(subtype.id);
          });
        }
      }
    });

    // Update form values
    setValue("imageTypeIds", Array.from(selectedTypeIds));
    setValue("imageSubTypeIds", Array.from(selectedSubTypeIds));
  }, [selectedNodeValues, allImageTypes, setValue]);

  return (
    <div className="flex flex-col gap-3">
      <div className="flex items-center">
        <Link href="/export-template">
          <MdOutlineArrowBackIosNew className="text-2xl mr-2" />
        </Link>

        <p className="font-bold text-2xl uppercase">
          Create an export template
        </p>
      </div>
      <Form
        onFinish={handleSubmit(onSubmit, (err) => {
          console.log(err);
        })}
        className="flex flex-col gap-2"
      >
        <div className="grid grid-cols-2 gap-3">
          <InputTextCommon
            label="Name"
            name="name"
            placeholder="Enter name"
            control={control}
            size="large"
            isRequired
          />
          <InputTextCommon
            label="Description"
            name="description"
            placeholder="Enter description"
            control={control}
            size="large"
            isRequired
          />
          <SelectCommon
            isRequired
            label="Project"
            control={control}
            name="projectId"
            onSearch={(value) => {
              setKeywordProject(value);
            }}
            searchValue={keywordProject}
            onBlur={() => {
              setKeywordProject("");
            }}
            allowClear
            options={projects.map((project) => ({
              label: project.name,
              value: project.id,
            }))}
            notFoundContent={
              loadingListProject ? <Spin size="small" /> : <>Not found</>
            }
            filterOption={false}
            showSearch
            placeholder="Select project"
            size="large"
            onPopupScroll={handleScrollProject}
            onClear={() => {
              setValue("prospectId", undefined as any);
            }}
          />
          <SelectCommon
            isRequired
            label=" Prospect"
            size="large"
            onClear={() => {
              setValue("drillholeIds", undefined as any);
            }}
            disabled={!selectedProjectId}
            control={control}
            name="prospectId"
            onSearch={(value) => {
              setKeywordProspect(value);
            }}
            searchValue={keywordProspect}
            options={prospects.map((prospect) => ({
              label: prospect.name,
              value: prospect.id,
            }))}
            onBlur={() => {
              setKeywordProspect("");
            }}
            notFoundContent={
              loadingListProspect ? <Spin size="small" /> : <>Not found</>
            }
            filterOption={false} // disable client-side filtering
            showSearch
            placeholder="Select prospect"
            onPopupScroll={handleScrollProspect}
            allowClear
          />
          <div className="col-span-2">
            <SelectCommon
              isRequired
              label=" Drillhole"
              size="large"
              mode="multiple"
              disabled={!selectedProjectId && !selectedProspectId}
              control={control}
              name="drillholeIds"
              onSearch={(value) => {
                setKeywordDrillhole(value);
              }}
              searchValue={keywordDrillhole}
              options={drillholes.map((drillhole) => ({
                value: drillhole.id,
                label: drillhole.name,
              }))}
              onBlur={() => {
                setKeywordDrillhole("");
              }}
              notFoundContent={
                loadingListDrillhole ? <Spin size="small" /> : <>Not found </>
              }
              filterOption={false} // disable client-side filtering
              showSearch
              placeholder="Select drillhole"
              onPopupScroll={handleScrollDrillhole}
              allowClear
            />
          </div>

          <div className="col-span-2 grid grid-cols-3 gap-3">
            <div className="grid grid-cols-3 col-span-12 gap-3">
              <div className="flex flex-col gap-2">
                <SelectCommon
                  isRequired
                  label="Drillhole Status"
                  size="large"
                  disabled={!selectedProjectId && !selectedProspectId}
                  control={control}
                  name="drillHoleStatus"
                  options={drillHoleStatusOptionsExport.map((d: any) => ({
                    label: d.label,
                    value: d.value,
                  }))}
                  filterOption={false} // disable client-side filtering
                  showSearch
                  allowClear
                  placeholder="Select drillhole status"
                />
                <p className="font-medium"></p>
              </div>
              <SelectCommon
                label="Update Status"
                size="large"
                control={control}
                name="updateStatus"
                placeholder="Select update status"
                options={drillHoleStatusOptionsExport.map((d: any) => ({
                  label: d.label,
                  value: d.value,
                }))}
                allowClear
                defaultValue={drillHoleStatusOptionsExport[0].value}
              />
              <SelectCommon
                label="Data output"
                size="large"
                control={control}
                name="dataOutput"
                defaultValue={DataOutputTypes[0].value}
                options={DataOutputTypes.map((item) => ({
                  label: item.label,
                  value: item.value,
                }))}
                defaultActiveFirstOption
                placeholder="Select data output"
              />
            </div>
          </div>

          <div className="col-span-2 grid grid-cols-3 gap-3">
            <SelectCommon
              name="dataSource"
              options={categoryOptionExport}
              control={control}
              label="Data Source"
              placeholder="Select data source"
              size="large"
              defaultValue={categoryOptionExport[0].value}
            />
            {dataSource === 1 && (
              <>
                <SelectCommon
                  name="imageSize"
                  options={imageSizeOptions}
                  control={control}
                  label="Image Size"
                  placeholder="Select image size"
                  mode="multiple"
                  size="large"
                  defaultValue={imageSizeOptions.map((item) => item.value)}
                />

                <SelectCommon
                  name="imageTypes"
                  options={dataSourceTypes}
                  control={control}
                  label="Image Source Type"
                  placeholder="Select image source type"
                  mode="multiple"
                  size="large"
                />
                {/* <ImageTypeCascade control={control} setValue={setValue} /> */}
                <div className="flex flex-col gap-2">
                  <div className="font-medium">Image Types & Subtypes</div>
                  <TreeSelect
                    size="large"
                    treeData={imageTypeTreeData}
                    value={treeSelectValue}
                    onChange={handleImageTypeTreeSelectChange}
                    multiple={true}
                    treeCheckable={true}
                    allowClear={true}
                    showSearch={false}
                    placeholder="Select image types and/or subtypes"
                    style={{ width: "100%" }}
                    showCheckedStrategy={TreeSelect.SHOW_PARENT}
                    loading={isFetchingImageType}
                    className="w-full h-full"
                    treeDefaultExpandAll // Optional: to show all nodes expanded by default
                  />
                </div>
              </>
            )}
          </div>
        </div>

        <div className="flex">
          <Button
            loading={loadingCreateExportTemplate || loadingUpdateExportTemplate}
            type="primary"
            htmlType="submit"
            size="large"
          >
            {id ? "Update" : "Create"}
          </Button>
        </div>
      </Form>
    </div>
  );
}
