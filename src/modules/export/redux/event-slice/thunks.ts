import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import { ExportTemplateQuery } from "../../interface/export-template.query";
import exportTemplateRequest from "../../api/export-template.api";

export const getEvent = createAppAsyncThunk(
  "event/list",
  async (query: ExportTemplateQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;

    const response = await exportTemplateRequest.getListEvent({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
    });
    return response;
  }
);
