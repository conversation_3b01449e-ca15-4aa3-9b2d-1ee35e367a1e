import z from "zod";

export const exportTemplateBody = z.object({
  id: z.any().optional(),
  name: z.string().trim().min(1, { message: "Required" }),
  description: z.string().trim().min(1, { message: "Required" }),
  projectId: z.number(),
  prospectId: z.number(),
  drillholeIds: z.array(z.number()),
  drillHoleStatus: z.number(),
  updateStatus: z.number(),
  dataSource: z.any(),
  dataOutput: z.number().optional(),
  imageSize: z.array(z.number()).optional(),
  imageCategory: z.array(z.number()).optional(),
  imageSubTypeIds: z.array(z.number()).optional(),
  imageTypeIds: z.array(z.number()).optional(),
  imageTypes: z.array(z.number()).optional(),
});

export type ExportTemplateBodyType = z.TypeOf<typeof exportTemplateBody>;
