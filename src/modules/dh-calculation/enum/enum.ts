export enum CalculationType {
  Block = 1,
  Distance = 2,
}

export const calculationTypeOptions = [
  {
    label: "Block",
    value: CalculationType.Block,
  },
  {
    label: "Distance",
    value: CalculationType.Distance,
  },
];

export enum CountingMethod {
  Structures = 1,
  RockSegments = 2,
}

export const countingMethodOptions = [
  {
    label: "Structures",
    value: CountingMethod.Structures,
  },
  {
    label: "Rock Segments",
    value: CountingMethod.RockSegments,
  },
];
