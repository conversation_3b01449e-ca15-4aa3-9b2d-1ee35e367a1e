import { useState } from "react";
import { toast } from "react-toastify";
import dhCalculationRequest from "../api/dh-calculation.api";
export const useExecuteDhCalculation = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: { drillholeId: number; rqdCalculationId: number },
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await dhCalculationRequest.executeCalculationRqd({
      ...params,
    });
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response.message);
      toast.error(response.message);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
