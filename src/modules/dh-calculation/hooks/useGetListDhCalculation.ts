import { useState } from "react";
import dhCalculationRequest from "../api/dh-calculation.api";
import { DhCalculationQuery } from "../interface/dh-calculation.query";

export const useGetListDhCalculation = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: DhCalculationQuery,
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await dhCalculationRequest.getList(params);
    if (response?.state === "success") {
      setData(response.data?.items);
      setLoading(false);
      onSuccess && onSuccess(response.data);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };
  return { request, loading, data };
};
