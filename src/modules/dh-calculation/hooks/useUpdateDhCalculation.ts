import { useState } from "react";
import { toast } from "react-toastify";
import dhCalculationRequest from "../api/dh-calculation.api";
import { DhCalculationBodyType } from "../model/schema/dh-calculation.schema";

export const useUpdateDhCalculation = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: DhCalculationBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await dhCalculationRequest.update({
      ...params,
    });
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response.message);
      toast.error(response.message);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
