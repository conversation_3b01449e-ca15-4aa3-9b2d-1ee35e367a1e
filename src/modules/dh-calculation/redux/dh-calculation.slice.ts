import { RequestState } from "@/common/configs/app.contants";
import { createSlice } from "@reduxjs/toolkit";
import { getDetailDhCalculation, getDhCalculation } from "./thunks";

const initialState: DhCalculationSliceState = {
  status: RequestState.idle,
  getDetailStatus: RequestState.idle,
};

export const dhCalculationSlice = createSlice({
  name: "dhCalculation",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getDhCalculation.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getDhCalculation.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      })
      .addCase(getDetailDhCalculation.pending, (state, action) => {
        state.getDetailStatus = RequestState.pending;
      })
      .addCase(getDetailDhCalculation.fulfilled, (state, action) => {
        state.getDetailStatus = RequestState.success;
        state.detail = action.payload;
      });
  },
});

export interface DhCalculationSliceState {
  result?: any;
  status: RequestState;
  getDetailStatus: RequestState;
  detail?: any;
}

export const {} = dhCalculationSlice.actions;
