import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import dhCalculationRequest from "../api/dh-calculation.api";
import { DhCalculationQuery } from "../interface/dh-calculation.query";

export const getDhCalculation = createAppAsyncThunk(
  "dhCalculation/list",
  async (query: DhCalculationQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await dhCalculationRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });

    return response;
  }
);

export const getDetailDhCalculation = createAppAsyncThunk(
  "dhCalculation/detail",
  async (id: string) => {
    const response = await dhCalculationRequest.getDetail(id);
    return response.data;
  }
);
