import { ModalType } from "@/common/configs/app.enum";
import { Button<PERSON>ommon } from "@/components/common/button-common";
import { InputNumberCommon } from "@/components/common/input-number";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { useGetListProject } from "@/modules/projects/hooks";
import { StructureSelectorType } from "@/modules/structure-type/enum/enum";
import structureRequest from "@/modules/structure/api/structure.api";
import { useGetListStructure } from "@/modules/structure/hooks/useGetListStructure";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, Form, Spin } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  CalculationType,
  calculationTypeOptions,
  CountingMethod,
  countingMethodOptions,
} from "../enum/enum";
import { useCreateDhCalculation } from "../hooks/useCreateDhCalculation";
import { useDeleteDhCalculation } from "../hooks/useDeleteDhCalculation";
import { useUpdateDhCalculation } from "../hooks/useUpdateDhCalculation";
import {
  DhCalculationBody,
  DhCalculationBodyType,
} from "../model/schema/dh-calculation.schema";
import { useGetDhCalculation } from "../hooks/useGetDhCalculation";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useExecuteDhCalculation } from "../hooks/useExecuteDhCalculation";
import dhCalculationRequest from "../api/dh-calculation.api";
import { TableCommon } from "@/components/common/table-common";
import { MdPlayArrow } from "react-icons/md";

export interface IModalDhCalculationProps {
  modalState: {
    isOpen: boolean;
    detailInfo: DhCalculationBodyType;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalDhCalculation(props: IModalDhCalculationProps) {
  const { modalState, setModalState, refresh } = props;

  const {
    request: requestCreateDhCalculation,
    loading: loadingCreateDhCalculation,
  } = useCreateDhCalculation();
  const {
    request: requestUpdateDhCalculation,
    loading: loadingUpdateDhCalculation,
  } = useUpdateDhCalculation();
  const {
    request: requestDeleteDhCalculation,
    loading: loadingDeleteDhCalculation,
  } = useDeleteDhCalculation();

  const { data: RqdCalculation, request: requestListRqdCalculation } =
    useGetDhCalculation();
  const { control, handleSubmit, getValues, setValue, watch, reset } =
    useForm<DhCalculationBodyType>({
      resolver: zodResolver(DhCalculationBody),
      defaultValues: {
        isActive: modalState.detailInfo?.isActive ?? true,
      },
    });

  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const isExecute = modalState.type === ModalType.EXECUTE;
  const onSubmit = (values: DhCalculationBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateDhCalculation(
        {
          ...values,
          interval:
            values.calculationType === CalculationType.Distance
              ? values.interval
              : null,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create rqd calculation successfully");
          refresh();
        }
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateDhCalculation(
        {
          ...values,
          id: modalState.detailInfo?.id,
          interval:
            modalState.detailInfo?.calculationType === CalculationType.Distance
              ? values.interval
              : null,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update rqd calculation successfully");
          refresh();
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteDhCalculation(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        toast.error(err?.message);
      }
    );
  };
  const calculationType = watch("calculationType");
  const countingMethod = watch("countingMethod");
  const [isInterval, setIsInterval] = useState(false);
  const [keywordStructureType, setKeywordStructureType] = useState("");
  const { data: listStructureType, request: requestListStructureType } =
    useGetListStructure();
  useEffect(() => {
    requestListStructureType({
      keyword: keywordStructureType,
      maxResultCount: 1000,
      skipCount: 0,
    });
  }, [keywordStructureType]);
  const [keywordProject, setKeywordProject] = useState("");
  const {
    data: listProject,
    request: requestListProject,
    loading: loadingProject,
  } = useGetListProject();
  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  useEffect(() => {
    requestListProject({
      keyword: keywordProject,
      maxResultCount: maxResultCountProject,
      skipCount: 0,
    });
  }, [keywordProject, maxResultCountProject]);
  useEffect(() => {
    if (modalState.detailInfo?.id) {
      requestListRqdCalculation(modalState.detailInfo?.id, (res) => {
        reset({
          ...res,
          projectIds: res.projects.map((item) => item.id),
          structureTypeId: res.structure?.id,
        });
      });
    }
  }, [modalState.detailInfo?.id]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const {
    request: requestExecuteCalculationRqd,
    loading: loadingExecuteCalculationRqd,
  } = useExecuteDhCalculation();

  const [dataExecute, setDataExecute] = useState<any>(null);
  const fetchDataExecute = () => {
    dhCalculationRequest
      .getRqdCalculationResult({
        RqdCalculationId: modalState.detailInfo.id,
        skipCount: (page - 1) * pageSize,
        maxResultCount: pageSize,
      })
      .then((res) => {
        setDataExecute(res.data);
      });
  };
  useEffect(() => {
    if (modalState.detailInfo?.id) {
      fetchDataExecute();
    }
  }, [modalState.detailInfo?.id, page, pageSize]);
  const handleExecute = () => {};
  const columns = [
    {
      title: "Drillhole",
      dataIndex: "drillHole",
      key: "drillHole",
      render: (value) => value?.name,
    },
    {
      title: "Depth From",
      dataIndex: "fromDepth",
      key: "fromDepth",
    },
    {
      title: "Depth To",
      dataIndex: "toDepth",
      key: "toDepth",
    },
    {
      title: "Total",
      dataIndex: "total",
      key: "total",
    },
    {
      title: "RQD Calculation",
      dataIndex: "rqdCalculation",
      key: "rqdCalculation",
      render: (value) => value?.name,
    },
  ];
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this rqd calculation?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the rqd
            calculation.
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteDhCalculation}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : isExecute ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-xl uppercase my-5">
            Rqd calculation result?
          </p>
          <div className="flex justify-end">
            <Button
              loading={loadingExecuteCalculationRqd}
              onClick={handleExecute}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
              type="primary"
              icon={<MdPlayArrow />}
            >
              Execute
            </Button>
          </div>
          <TableCommon
            columns={columns}
            dataSource={dataExecute?.items}
            pagination={{
              total: dataExecute?.totalCount,
              current: page,
              pageSize: pageSize,
              onChange: (page, pageSize) => {
                setPage(page);
                setPageSize(pageSize);
              },
            }}
          />
          <div className="flex justify-end gap-2"></div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update RQD Calculation"
              : "Add RQD Calculation"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (err) => {})}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Calculation Name"
              name="name"
              placeholder="Type rqd calculation name here"
              control={control}
              isRequired={true}
            />
            <SelectCommon
              label="Calculation Type"
              name="calculationType"
              placeholder="Select calculation type"
              control={control}
              isRequired={true}
              options={calculationTypeOptions}
            />
            {calculationType === CalculationType.Distance && (
              <InputNumberCommon
                label="Interval"
                name="interval"
                placeholder="Enter interval"
                control={control}
                isRequired={true}
              />
            )}
            <SelectCommon
              label="Counting Method"
              name="countingMethod"
              placeholder="Select counting method"
              control={control}
              isRequired={true}
              options={countingMethodOptions}
            />
            {countingMethod === CountingMethod.Structures && (
              <>
                <SelectCommon
                  label="Structure Type"
                  name="structureTypeId"
                  placeholder="Select structure type"
                  control={control}
                  options={listStructureType?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  searchValue={keywordStructureType}
                  onSearch={(value) => {
                    setKeywordStructureType(value);
                  }}
                  onBlur={() => {
                    setKeywordStructureType("");
                  }}
                  showSearch
                  filterOption={false}
                  onChange={async (value) => {
                    setKeywordStructureType("");
                    const res = await structureRequest.getDetail(value);
                    const isInterval =
                      res?.data?.selector === StructureSelectorType.Interval;
                    setIsInterval(isInterval);
                  }}
                />
              </>
            )}
            {isInterval && (
              <InputNumberCommon
                label="Minimum Width"
                name="minimumWidth"
                placeholder="Enter minimum width"
                control={control}
              />
            )}
            {countingMethod === CountingMethod.RockSegments && (
              <>
                <InputNumberCommon
                  label="Minimum Width"
                  name="minimumWidth"
                  placeholder="Enter minimum width"
                  control={control}
                  isRequired={true}
                />
              </>
            )}

            <SelectCommon
              label="Project"
              name="projectIds"
              placeholder="Select project"
              control={control}
              isRequired={true}
              options={listProject?.map((item) => ({
                label: item.name,
                value: item.id,
              }))}
              searchValue={keywordProject}
              onPopupScroll={handleScrollProject}
              showSearch
              filterOption={false}
              onChange={(value) => {
                setKeywordProject("");
              }}
              onSearch={(value) => {
                setKeywordProject(value);
              }}
              mode="multiple"
              notFoundContent={
                loadingProject ? <Spin size="small" /> : <>Not found</>
              }
            />
            <ToogleCommon label="Active" name="isActive" control={control} />
            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={
                  loadingCreateDhCalculation || loadingUpdateDhCalculation
                }
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update RQD Calculation"
                  : "Add RQD Calculation"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
