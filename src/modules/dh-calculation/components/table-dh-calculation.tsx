"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { IPaginationResponse } from "@/common/interfaces/response/IPaginationResponse";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import CheckCircleOutlined, {
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import { Tag, type TableColumnsType } from "antd";
import { debounce } from "lodash";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";
import { CalculationType, CountingMethod } from "../enum/enum";
import { useExecuteDhCalculation } from "../hooks/useExecuteDhCalculation";
import { getDhCalculation } from "../redux/thunks";
import { ModalDhCalculation } from "./modal-dh-calculation";
const TableDhCalculation: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();

  const buttons = [
    {
      title: "Active",
      isActveString: "true",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "true");
        params.set("page", "1");
        replace(`${pathname}?${params.toString()}`);
      },
    },
    {
      title: "Inactive",
      isActveString: "false",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "false");
        params.set("page", "1");
        replace(`${pathname}?${params.toString()}`);
      },
    },
    {
      title: "All",
      isActveString: undefined,
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("page", "1");
        params.delete("isActive");
        replace(`${pathname}?${params.toString()}`);
      },
    },
  ];
  const dhCalculation = useAppSelector((state) => state.dhCalculation);

  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: dhCalculation?.result?.items ?? [],
      reduxTablePagination: {
        total: dhCalculation?.result?.pagination.total ?? 0,
      } as IPaginationResponse,
      requestState: dhCalculation?.status ?? RequestState.idle,
      getDataAction: getDhCalculation,
      filter: {
        page: 1,
        pageSize: 10,
        ...(searchParams.has("isActive")
          ? { isActive: searchParams.get("isActive") }
          : {}),
      },
    });

  //STATE
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });
  const {
    request: executeCalculationRqd,
    loading: loadingExecuteCalculationRqd,
  } = useExecuteDhCalculation();
  //table
  const columns: TableColumnsType<any> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
            {/* <Tooltip title="Execute calculation rqd">
              <GiProcessor
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "execute",
                    detailInfo: record,
                  })
                }
                className="hover:text-primary cursor-pointer"
                style={{ fontSize: 16 }}
              />
            </Tooltip> */}
          </div>
        );
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render(value, index) {
        return <p key={index}>{value}</p>;
      },
    },
    {
      title: "Calculation Type",
      dataIndex: "calculationType",
      key: "calculationType",
      render(value, index) {
        switch (value) {
          case CalculationType.Block:
            return <p key={index}>Block</p>;
          case CalculationType.Distance:
            return <p key={index}>Distance</p>;

          default:
            return null;
        }
      },
    },
    {
      title: "Interval",
      dataIndex: "interval",
      key: "interval",
    },
    {
      title: "Counting Method",
      dataIndex: "countingMethod",
      key: "countingMethod",
      render(value, index) {
        switch (value) {
          case CountingMethod.RockSegments:
            return <p key={index}>Rock Segments</p>;
          case CountingMethod.Structures:
            return <p key={index}>Structures</p>;
          default:
            return null;
        }
      },
    },
    {
      title: "Minimum Width",
      dataIndex: "minimumWidth",
      key: "minimumWidth",
    },
    {
      title: "Structure Type",
      dataIndex: "structureType",
      key: "structureType",
      render(value, record, index) {
        return <p key={index}>{value?.name}</p>;
      },
    },
    // {
    //   title: "Minimum Segment Length",
    //   dataIndex: "minimumSegmentLength",
    //   key: "minimumSegmentLength",
    // },

    {
      title: "Is Active",
      dataIndex: "isActive",
      key: "isActive",
      render(value, index) {
        return value ? (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
          >
            Active
          </Tag>
        ) : (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Inactive
          </Tag>
        );
      },
    },
  ];

  const updateSearchParams = useCallback(
    debounce((keyword) => {
      const params = new URLSearchParams(queries);
      const currentParams = new URLSearchParams(window.location.search);
      keyword
        ? currentParams.set("keyword", keyword)
        : currentParams.delete("keyword");
      currentParams.set("page", "1");
      // Preserve isActive filter when searching
      const isActive = searchParams.get("isActive");
      if (isActive) {
        currentParams.set("isActive", isActive);
      }
      router.replace(`${window.location.pathname}?${currentParams.toString()}`);
    }, 300),
    [queries, router]
  );
  useEffect(() => {
    if (dhCalculation.result?.items.length === 0) {
      if (tablePagination.current && tablePagination.current - 1 > 0) {
        refresh({
          page: tablePagination.current - 1,
        });
        const params = new URLSearchParams(queries);
        params.set("page", (tablePagination.current - 1).toString());
        router.replace(`${window.location.pathname}?${params.toString()}`);
      }
    }
  }, [dhCalculation.result?.items]);

  useEffect(() => {
    if (!window.location.search) {
      const params = new URLSearchParams();
      params.set("isActive", "true");
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    }
  }, []);

  return (
    <>
      {modalState.isOpen && (
        <ModalDhCalculation
          refresh={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold">RQD Calculations</p>
        <hr />
        <div className="">
          <div className="flex justify-between gap-2">
            <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[400px] bg-white border">
              <IconSearch />
              <input
                type="text"
                placeholder="Search"
                className="w-full font-normal outline-none text-primary placeholder:text-gray80"
                onChange={(e) => {
                  updateSearchParams(e.target.value);
                }}
                defaultValue={queries.keyword}
              />
            </div>
            <div className="flex gap-2">
              {buttons.map((button, index) => {
                let className: string = "";
                const isActiveSearchParam = searchParams.get("isActive");
                if (isActiveSearchParam === null && button.title === "All") {
                  className = "btn-primary btn-active";
                }
                if (isActiveSearchParam === button.isActveString) {
                  className = "btn-primary btn-active";
                }
                return (
                  <button
                    key={index}
                    className={`btn btn-sm ${className}`}
                    onClick={button.onclick}
                  >
                    {button.title}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
        <TableCommon
          className="font-visby"
          rowKey={(record) => (record as any).id}
          pagination={tablePagination}
          loading={dhCalculation.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={dhCalculation.result?.items}
          footer={() => (
            <div className="justify-center my-2 ">
              <button
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  })
                }
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  Add RQD Calculation
                </span>
              </button>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableDhCalculation;
