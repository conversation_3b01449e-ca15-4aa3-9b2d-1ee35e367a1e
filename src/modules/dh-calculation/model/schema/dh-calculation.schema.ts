import z from "zod";

export const DhCalculationBody = z.object({
  id: z.any().optional(),
  name: z.string().trim().min(1, { message: "Required" }),
  isActive: z.boolean().optional(),
  calculationType: z.number().optional().nullable(),
  countingMethod: z.number().optional().nullable(),
  interval: z.number().optional().nullable(),
  structureTypeId: z.number().optional().nullable(),
  minimumWidth: z.number().optional().nullable(),
  minimumSegmentLength: z.number().optional().nullable(),
  projectIds: z.array(z.number()).optional().nullable(),
});

export type DhCalculationBodyType = z.TypeOf<typeof DhCalculationBody>;
