import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";
import { DhCalculationQuery } from "../interface/dh-calculation.query";
import { DhCalculationBodyType } from "../model/schema/dh-calculation.schema";

const dhCalculationRequest = {
  getList: async (params: DhCalculationQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/RqdCalculation/GetAll`,
        params
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10)
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getDetail: async (id: string) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/RqdCalculation/Get?Id=${id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  create: async (body: DhCalculationBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/RqdCalculation/Create`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  delete: async (params: { id: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/RqdCalculation/Delete?Id=${params.id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: DhCalculationBodyType) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/RqdCalculation/Update`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getRqdCalculationResult: async (params: {
    RqdCalculationId: string;
    DrillHoleId?: number;
    skipCount?: number;
    maxResultCount?: number;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/RqdCalculation/GetRqdCalculationResult`,
        params
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  executeCalculationRqd: async (params: {
    drillholeId: number;
    rqdCalculationId?: number;
  }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/RqdCalculation/ExecuteRqdCalculationByDrillHole`,
        params
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  calculateTrayDepth: async (params: { drillHoleId: number }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/DrillHole/CalculationTrayDepth`,
        params
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default dhCalculationRequest;
