"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { IPaginationResponse } from "@/common/interfaces/response/IPaginationResponse";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import { KeyCode } from "@/modules/tenant/const/enum.tenant";
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import type { TableColumnsType } from "antd";
import { Tag } from "antd";
import { debounce } from "lodash";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useState } from "react";
import { toast } from "react-toastify";
import { getApiKey } from "../redux/thunks";
import { ModalApiKey } from "./modal-api-key";

const TableApiKey: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });
  const apiKey = useAppSelector((state) => state.apiKey);

  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: apiKey?.result?.items ?? [],
      reduxTablePagination: {
        total: apiKey?.result?.pagination.total ?? 0,
      } as IPaginationResponse,
      requestState: apiKey?.status ?? RequestState.idle,
      getDataAction: getApiKey,
      filter: {
        page: 1,
        pageSize: 10,
      },
    });

  //STATE
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });

  const [visibleKeys, setVisibleKeys] = useState<{ [key: string]: boolean }>(
    {}
  );

  //table
  const columns: TableColumnsType<any> = [
    {
      title: "Account",
      dataIndex: "tenant",
      key: "tenant",
      render(value, index) {
        return <p key={index}>{value?.tenancyName}</p>;
      },
    },
    {
      title: "Key",
      dataIndex: "key",
      key: "key",
      render(value, record) {
        const isVisible = visibleKeys[record.id];
        const displayValue = isVisible ? value : "••••••••••••••••";

        return (
          <div className="flex items-center gap-2">
            <p className="font-mono">{displayValue}</p>
            <div className="flex items-center gap-1">
              {isVisible ? (
                <EyeInvisibleOutlined
                  className="cursor-pointer hover:text-primary"
                  onClick={() => {
                    setVisibleKeys((prev) => ({ ...prev, [record.id]: false }));
                  }}
                />
              ) : (
                <EyeOutlined
                  className="cursor-pointer hover:text-primary"
                  onClick={() => {
                    setVisibleKeys((prev) => ({ ...prev, [record.id]: true }));
                  }}
                />
              )}
              <CopyOutlined
                className="cursor-pointer hover:text-primary"
                onClick={() => {
                  navigator.clipboard.writeText(value);
                  toast.success("Copied to clipboard", {
                    position: "top-center",
                    autoClose: 500,
                  });
                }}
              />
            </div>
          </div>
        );
      },
    },
    {
      title: "Key Code",
      dataIndex: "apiKeyType",
      key: "apiKeyType",
      render(apiKeyTypes, record, index) {
        const getTagColor = (code: number) => {
          switch (code) {
            case 1: // Upload Image
              return "blue";
            case 2: // Download Image
              return "cyan";
            case 3: // Get Context
              return "green";
            case 4: // Get Drillhole
              return "purple";
            case 5: // Update Drillhole
              return "magenta";
            case 6: // Get Image Row Data
              return "orange";
            case 7: // Get All Image
              return "geekblue";
            case 8: // Create Drillhole
              return "volcano";
            case 9: // Process Image
              return "gold";
            default:
              return "default";
          }
        };

        const getDescription = (code: number) => {
          switch (code) {
            case 1:
              return "Upload Image";
            case 2:
              return "Download Image";
            case 3:
              return "Get Context";
            case 4:
              return "Get Drillhole";
            case 5:
              return "Update Drillhole";
            case 6:
              return "Get Image Row Data";
            case 7:
              return "Get All Image";
            case 8:
              return "Create Drillhole";
            case 9:
              return "Process Image";
            default:
              return "Unknown";
          }
        };

        return (
          <div className="flex flex-wrap gap-1">
            {Array.isArray(apiKeyTypes) &&
              apiKeyTypes.map((type, idx) => (
                <Tag key={`${index}-${idx}`} color={getTagColor(type.code)}>
                  {getDescription(type.code)}
                </Tag>
              ))}
          </div>
        );
      },
    },
    {
      title: "Expires On",
      dataIndex: "expiresAt",
      key: "expiresAt",
      render(value, record, index) {
        if (!record?.isUnlimited) {
          const date = new Date(value);
          const formattedDate = date.toLocaleString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
          });
          return <p key={index}>{formattedDate}</p>;
        } else {
          return <Tag color="purple">Unlimited</Tag>;
        }
      },
    },

    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      render(value, index) {
        return (
          <Tag key={index} color={value ? "green" : "red"}>
            {value ? "Active" : "Inactive"}
          </Tag>
        );
      },
    },
    // {
    //   title: "Is Expired",
    //   dataIndex: "isExpried",
    //   key: "isExpried",
    //   render(value, record, index) {
    //     switch (value) {
    //       case false:
    //         return <Tag color="green">Yes</Tag>;
    //       case true:
    //         return <Tag color="red">No</Tag>;
    //       default:
    //         return <p key={index}>Unknown</p>;
    //     }
    //   },
    // },

    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },
  ];

  const updateSearchParams = useCallback(
    debounce((keyword) => {
      const params = new URLSearchParams(queries);
      keyword ? params.set("tenantName", keyword) : params.delete("tenantName");
      params.set("page", "1");
      router.replace(`${window.location.pathname}?${params.toString()}`);
    }, 300),
    [queries, router]
  );
  const fontSize = useAppSelector((state) => state.user.fontSize);
  return (
    <>
      {modalState.isOpen && (
        <ModalApiKey
          refresh={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold">Api Keys</p>
        <hr />
        <div className="">
          <div className="grid grid-cols-2 gap-2">
            <div className="px-5 py-2 rounded-lg flex items-center gap-2 h-[38px bg-white border">
              <IconSearch />
              <input
                type="text"
                placeholder="Search"
                className="w-full font-normal outline-none text-primary placeholder:text-gray80"
                onChange={(e) => {
                  updateSearchParams(e.target.value);
                }}
                defaultValue={queries.tenantName}
              />
            </div>
          </div>
        </div>
        <TableCommon
          style={{
            fontSize: `${fontSize}px`,
          }}
          rowKey={(record) => (record as any).id}
          pagination={tablePagination}
          loading={apiKey.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={apiKey.result?.items}
          footer={() => (
            <div className="justify-center my-2 ">
              <button
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  })
                }
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  Add Api Key
                </span>
              </button>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableApiKey;
