import { ModalType } from "@/common/configs/app.enum";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { PERMISSIONS } from "@/constants/general.const";
import { getApiKeyType } from "@/modules/api-key-type/redux/thunks";
import { selectTenant } from "@/modules/tenant/redux/selectors";
import { getTenants } from "@/modules/tenant/redux/thunks";
import { zodResolver } from "@hookform/resolvers/zod";
import { DatePicker, Form } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateApiKey } from "../hooks/useCreateApiKey";
import { useDeleteApiKey } from "../hooks/useDeleteApiKey";
import { useUpdateApiKey } from "../hooks/useUpdateApiKey";
import { ApiKeyBody, ApiKeyBodyType } from "../model/schema/ api-key.schema";
import { APIKeyCode } from "../model/schema/enum.api-key";
import { number } from "zod";

export interface IModalApiKeyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalApiKey(props: IModalApiKeyProps) {
  const { modalState, setModalState, refresh } = props;
  const { request: requestCreateApiKey, loading: loadingCreateApiKey } =
    useCreateApiKey();
  const { request: requestUpdateApiKey, loading: loadingUpdateApiKey } =
    useUpdateApiKey();
  const { request: requestDeleteApiKey, loading: loadingDeleteApiKey } =
    useDeleteApiKey();

  // Get user info for role-based access control
  const userInfo = useAppSelector((state) => state.user.userInfo);
  const isAdmin = userInfo?.roles?.includes(PERMISSIONS.Admin.value);
  const isCompany = userInfo?.roles?.includes(PERMISSIONS.Company.value);
  const userTenantId = Number(userInfo?.tenantId);

  const { control, handleSubmit, setValue, getValues } =
    useForm<ApiKeyBodyType>({
      resolver: zodResolver(ApiKeyBody),
      defaultValues: {
        isActive: true,
        ...modalState?.detailInfo,
        tenantId:
          isCompany && userTenantId
            ? userTenantId
            : modalState?.detailInfo?.tenant?.id,
        keyCode: modalState?.detailInfo?.apiKeyType?.map((item) => item.id),
      },
    });
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const onSubmit = (values: ApiKeyBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateApiKey(
        values,
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create API key successfully");
          refresh();
        },
        (error) => {
          console.log("error: ", error);
          toast.error(`Failed to create API key: ${error || "Unknown error"}`);
        }
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateApiKey(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update API key successfully");
          refresh();
        },
        (error) => {
          console.log("error: ", error);
          toast.error(`Failed to update API key: ${error || "Unknown error"}`);
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteApiKey(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        toast.success("Delete API key successfully");
        refresh();
      },
      (error) => {
        toast.error(`Failed to delete API key: ${error || "Unknown error"}`);
      }
    );
  };
  const tenants = useAppSelector(selectTenant);
  const [keyword, setKeyword] = useState<string>("");
  const dispatch = useAppDispatch();
  // Keep a reference to the previous modalState.type to detect changes
  const [prevModalType, setPrevModalType] = useState<string | null>(null);

  useEffect(() => {
    // Set the tenant ID for company users on form load
    if (isCompany && userTenantId && modalState.type === "create") {
      setValue("tenantId", userTenantId);
    }

    // Reset form state when modal type changes
    if (modalState.type !== prevModalType) {
      setPrevModalType(modalState.type);

      // Clear any previous form errors
      if (modalState.type === "create") {
        // Reset necessary fields for a new create operation
        setValue("keyCode", []);
        setValue("isActive", true);
        if (isCompany && userTenantId) {
          setValue("tenantId", userTenantId);
        }
      }
    }

    dispatch(
      getTenants({
        isActive: true,
        keyword: keyword,
        pageSize: 1000,
      })
    );
  }, [
    keyword,
    modalState.type,
    prevModalType,
    isCompany,
    userTenantId,
    setValue,
  ]);
  useEffect(() => {
    const isUnlimited = modalState.detailInfo?.isUnlimited;
    if (isUnlimited) {
      setValue("expiresAt", undefined);
    }
  }, [modalState.detailInfo]);
  const _isUnlimited = modalState.detailInfo?.isUnlimited;
  const [isUnlimited, setIsUnlimited] = useState<boolean>(_isUnlimited);
  const apiKeyType = useAppSelector((state) => state.apiKeyType);

  useEffect(() => {
    dispatch(getApiKeyType({}));
  }, []);
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this api key?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the api
            key
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteApiKey}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update api key"
              : "Add api key"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (errors) => {
              console.log(errors);
            })}
            className="flex flex-col gap-3"
          >
            {/* For Admin users, show all tenants. For Company users, restrict to their own tenant */}
            {isAdmin ? (
              <SelectCommon
                label="Account"
                name="tenantId"
                placeholder="Select account"
                control={control}
                options={tenants?.result?.items?.map((item) => ({
                  label: item.tenancyName,
                  value: item.id,
                }))}
                searchValue={keyword}
                onSearch={(value) => setKeyword(value)}
                showSearch
                filterOption={false}
              />
            ) : (
              <SelectCommon
                label="Account"
                name="tenantId"
                placeholder="Select account"
                control={control}
                options={tenants?.result?.items
                  ?.filter((item) =>
                    isCompany ? item.id === userTenantId : true
                  )
                  .map((item) => ({
                    label: item.tenancyName,
                    value: item.id,
                  }))}
                disabled={isCompany} // Disable for company users as they can only use their own tenant
              />
            )}
            <SelectCommon
              label="Key Code"
              mode="multiple"
              name="keyCode"
              placeholder="Select key code"
              control={control}
              options={apiKeyType.result?.items?.map((item) => ({
                label: APIKeyCode[item.code],
                value: item.code,
              }))}
            />
            <div className="flex flex-col gap-3">
              <p className="font-medium">Expires On</p>
              <Controller
                control={control}
                name={`expiresAt`}
                render={({ field }) => (
                  <DatePicker
                    {...field}
                    value={
                      isUnlimited
                        ? null
                        : field.value
                        ? dayjs(field.value)
                        : null
                    }
                    // value={field.value ? dayjs(field.value) : null}
                    onChange={(date) => {
                      if (date) {
                        setIsUnlimited(false);
                        field.onChange(date ? date.format("YYYY-MM-DD") : null);
                      } else {
                        setIsUnlimited(true);
                        field.onChange(undefined);
                      }
                    }}
                  />
                )}
              />
            </div>

            <ToogleCommon control={control} name="isActive" label="Is Active" />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={loadingCreateApiKey || loadingUpdateApiKey}
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update Api Key"
                  : "Add Api Key"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
