import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import apiKeyRequest from "../api/api-key.api";
import { ApiKeyQuery } from "../interface/api-key.query";

export const getApiKey = createAppAsyncThunk(
  "apiKey/list",
  async (query: ApiKeyQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await apiKeyRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });
    return response;
  }
);

export const getApiKeyByTenant = createAppAsyncThunk(
  "apiKey/listByTenant",
  async (query: ApiKeyQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await apiKeyRequest.getApiKeyByTenant({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });
    return response;
  }
);
