import { RequestState } from "@/common/configs/app.contants";
import { createSlice } from "@reduxjs/toolkit";
import { getApiKey, getApiKeyByTenant } from "./thunks";

const initialState: AccountSettingsSliceState = {
  status: RequestState.idle,
};

export const apiKeySlice = createSlice({
  name: "apiKey",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getApiKey.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getApiKey.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      })
      .addCase(getApiKeyByTenant.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getApiKeyByTenant.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      });
  },
});

export interface AccountSettingsSliceState {
  result?: any;
  status: RequestState;
}

export const {} = apiKeySlice.actions;
