import { useState } from "react";
import { ApiKeyBodyType } from "../model/schema/ api-key.schema";
import apiKeyRequest from "../api/api-key.api";
import { RequestState } from "@/common/configs/app.contants";

export const useCreateApiKey = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ApiKeyBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await apiKeyRequest.create(params);
    console.log("response: ", response);
    if (response.state === RequestState.success) {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response.message);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
