import { useState } from "react";
import apiKeyRequest from "../api/api-key.api";
import { RequestState } from "@/common/configs/app.contants";

export const useDeleteApiKey = () => {
  const [loading, setLoading] = useState(false);

  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await apiKeyRequest.delete(params);
    if (response.state === RequestState.success) {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response.message);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
