import { useState } from "react";
import apiKeyRequest from "../api/api-key.api";
import { ApiKeyBodyType } from "../model/schema/ api-key.schema";
import { RequestState } from "@/common/configs/app.contants";

export const useUpdateApiKey = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ApiKeyBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await apiKeyRequest.update(params);
    if (response.state === RequestState.success) {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response.message);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
