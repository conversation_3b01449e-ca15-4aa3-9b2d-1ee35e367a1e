import { ModalType } from "@/common/configs/app.enum";
import { Button<PERSON><PERSON><PERSON> } from "@/components/common/button-common";
import { InputNumberCommon } from "@/components/common/input-number";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import assayRequest from "@/modules/assay/api/assay.api";
import { useGetListAssaySuiteByLoggingView } from "@/modules/assay/hooks/useGetListAssaySuiteByLoggingView";
import attributeRequest from "@/modules/downhole-point/api/attributes.api";
import { useGetSuiteByLoggingView } from "@/modules/downhole-point/hooks/useGetSuiteByLoggingView";
import geologySuiteRequest from "@/modules/geology-suite/api/geology-suite.api";
import { useGetGeologySuiteByLoggingView } from "@/modules/geology-suite/hooks/useGetGeologySuiteByLoggingView";
import { useQueryImageSubType } from "@/modules/image-type/hooks/useGetQueryImageSubType";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import { useGetListNumberRange } from "@/modules/number-range/hooks/useGetListNumberRange";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { ColumnClass, columnClassOptions } from "../enum/enum";
import { useCreateLoggingViewColumn } from "../hooks/useCreateLoggingView";
import { useDeleteLoggingViewColumn } from "../hooks/useDeleteLoggingView";
import { useGetLoggingViewColumn } from "../hooks/useGetLoggingView";
import { useUpdateLoggingViewColumn } from "../hooks/useUpdateLoggingView";
import {
  LoggingViewBodyDetail,
  LoggingViewBodyDetailType,
} from "../model/schema/logging-view.schema";

export interface IModalLoggingViewDetailProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalLoggingViewDetail(props: IModalLoggingViewDetailProps) {
  const params = useParams();
  const id = params.id[0];

  const { modalState, setModalState, refresh } = props;

  const [suiteOptions, setSuiteOptions] = useState([]);
  const [attributeOptions, setAttributeOptions] = useState([]);
  const {
    request: requestCreateLoggingViewColumn,
    loading: loadingCreateLoggingViewColumn,
  } = useCreateLoggingViewColumn();
  const {
    request: requestUpdateLoggingViewColumn,
    loading: loadingUpdateLoggingViewColumn,
  } = useUpdateLoggingViewColumn();
  const {
    request: requestDeleteLoggingView,
    loading: loadingDeleteLoggingView,
  } = useDeleteLoggingViewColumn();
  const { control, handleSubmit, watch, setValue, reset } =
    useForm<LoggingViewBodyDetailType>({
      resolver: zodResolver(LoggingViewBodyDetail),
    });
  useEffect(() => {
    const getSuiteId = () => {
      switch (modalState?.detailInfo?.columnClass) {
        case ColumnClass.Geology:
          return modalState?.detailInfo?.geologySuite?.id;
        case ColumnClass.Assay:
          return modalState?.detailInfo?.assaySuite?.id;
        case ColumnClass.Geophysics:
          return modalState?.detailInfo?.suite?.id;
        default:
          return undefined;
      }
    };

    const getFieldId = () => {
      switch (modalState?.detailInfo?.columnClass) {
        case ColumnClass.Geology:
          return modalState?.detailInfo?.geologySuiteField?.id;
        case ColumnClass.Assay:
          return modalState?.detailInfo?.assayAttribute?.id;
        case ColumnClass.Geophysics:
          return modalState?.detailInfo?.attribute?.id;
        default:
          return undefined;
      }
    };

    const formData = {
      ...modalState?.detailInfo,
      suiteId: getSuiteId(),
      fieldId: getFieldId(),
      isActive: modalState?.detailInfo?.isActive ?? true,
      imageTypeId: modalState?.detailInfo?.imageType?.id,
      imageSubTypeId: modalState?.detailInfo?.imageSubtype?.id,
    };

    reset(formData);

    // Initialize data loading for update mode
    if (modalState?.type === ModalType.UPDATE && formData.columnClass) {
      loadSuiteOptions(formData.columnClass as ColumnClass);
      if (formData.suiteId) {
        loadAttributeOptions(
          formData.columnClass as ColumnClass,
          formData.suiteId
        );
      }
    }
  }, [modalState?.detailInfo]);
  const { request: requestGetLoggingViewColumnDetail } =
    useGetLoggingViewColumn();
  useEffect(() => {
    const id = modalState?.detailInfo?.id;
    if (id) {
      requestGetLoggingViewColumnDetail(id, (res) => {
        if (res?.assayAttributes?.[0]) {
          setValue("fieldId", res.assayAttributes[0].id);
        }
        setValue("numberRangeId", res.numberRange?.id);
      });
    }
  }, [modalState?.detailInfo?.id]);

  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const onSubmit = (values: LoggingViewBodyDetailType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateLoggingViewColumn(
        {
          ...values,
          loggingViewId: Number(id),
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create logging view successfully");
          refresh();
        }
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateLoggingViewColumn(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update logging view successfully");
          refresh();
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteLoggingView(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        toast.error(err?.message);
      }
    );
  };

  const { request: requestGetListGeophysicsSuiteByLoggingView } =
    useGetSuiteByLoggingView();

  const { request: requestGetGeologySuiteByLoggingView } =
    useGetGeologySuiteByLoggingView();
  const { request: requestGetListAssaySuiteByLoggingView } =
    useGetListAssaySuiteByLoggingView();
  const columnClass = watch("columnClass");
  const loadSuiteOptions = async (columnClass: ColumnClass) => {
    if (columnClass === ColumnClass.Geology) {
      requestGetGeologySuiteByLoggingView(
        {
          loggingViewId: Number(id),
          skipCount: 0,
          maxResultCount: 1000,
        },
        (res) => {
          setSuiteOptions(
            res?.items?.map((item) => ({
              label: item.name,
              value: item.id,
            }))
          );
        }
      );
    }
    if (columnClass === ColumnClass.Assay) {
      requestGetListAssaySuiteByLoggingView(
        {
          loggingViewId: Number(id),
          skipCount: 0,
          maxResultCount: 1000,
        },
        (res) => {
          setSuiteOptions(
            res?.items?.map((item) => ({
              label: item.name,
              value: item.id,
            }))
          );
        }
      );
    }
    if (columnClass === ColumnClass.Geophysics) {
      requestGetListGeophysicsSuiteByLoggingView(
        {
          loggingViewId: Number(id),
          skipCount: 0,
          maxResultCount: 1000,
        },
        (res) => {
          setSuiteOptions(
            res?.items?.map((item) => ({
              label: item.name,
              value: item.id,
            }))
          );
        }
      );
    }
  };

  const loadAttributeOptions = async (
    columnClass: ColumnClass,
    suiteId: number
  ) => {
    if (columnClass === ColumnClass.Geology && suiteId) {
      const res = await geologySuiteRequest.getDetail(suiteId.toString());
      setAttributeOptions(
        res.data?.geologySuiteFields?.map((item: any) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
    if (columnClass === ColumnClass.Assay && suiteId) {
      const res = await assayRequest.getAssaySuite({
        Id: suiteId as any,
      });
      setAttributeOptions(
        res.data?.assayAttributes?.map((item: any) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
    if (columnClass === ColumnClass.Geophysics && suiteId) {
      const res = await attributeRequest.getAttributeBySuiteId({
        Id: suiteId as any,
      });
      setAttributeOptions(
        res.data?.map((item: any) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
  };
  const { data: listNumberRange, request: requestGetListNumberRange } =
    useGetListNumberRange();
  const [keywordNumberRange, setKeywordNumberRange] = useState("");

  useEffect(() => {
    requestGetListNumberRange({
      skipCount: 0,
      keyword: keywordNumberRange,
      maxResultCount: 1000,
    });
  }, [keywordNumberRange]);
  const { data: imageTypes } = useQueryImageType();
  const imageCategoryOptions = useMemo(
    () =>
      imageTypes?.data?.items.map((item) => ({
        label: item.name,
        value: item.id,
      })) || [],
    [imageTypes?.data?.items]
  );
  const imageTypeId = watch("imageTypeId");

  const { data: imageSubTypes, setSearchParams: setSearchParamsImageSubType } =
    useQueryImageSubType();
  const imageSubTypeOptions = useMemo(() => {
    if (imageTypeId) {
      return (
        imageSubTypes?.data?.items.map((item) => ({
          label: item.name,
          value: item.id,
        })) || []
      );
    }
    return [];
  }, [imageSubTypes?.data?.items, imageTypeId]);
  useEffect(() => {
    if (imageTypeId) {
      setSearchParamsImageSubType({
        imageTypeId: imageTypeId,
      });
    } else {
      // Clear imageSubTypes data when imageTypeId is cleared
      setSearchParamsImageSubType({
        imageTypeId: undefined,
      });
    }
    // Reset imageSubTypeId when imageTypeId changes (both when set and when cleared)
    setValue("imageSubTypeId", undefined);
  }, [imageTypeId, setValue, setSearchParamsImageSubType]);
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this column?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            column.
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteLoggingView}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update logging view column"
              : "Add logging view column"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (err) => {
              console.log(err);
            })}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type column name here"
              control={control}
              isRequired={true}
            />

            <InputNumberCommon
              label="Sequence"
              name="sequence"
              placeholder="Type sequence number here"
              control={control}
              isRequired={true}
            />

            <SelectCommon
              label="Column Class"
              name="columnClass"
              placeholder="Select column class"
              control={control}
              isRequired={true}
              options={columnClassOptions}
              onChange={(value) => {
                const currentValues = watch();
                reset({
                  ...currentValues,
                  columnClass: value,
                  suiteId: undefined,
                  fieldId: undefined,
                  imageTypeId: undefined,
                  imageSubTypeId: undefined,
                });
                setSuiteOptions([]);
                setAttributeOptions([]);
                if (value) {
                  loadSuiteOptions(value as ColumnClass);
                }
              }}
            />

            {columnClass === ColumnClass.CoreRow && (
              <>
                <SelectCommon
                  control={control}
                  options={imageCategoryOptions}
                  placeholder="Image Type"
                  allowClear
                  name="imageTypeId"
                  label="Image Type"
                />
                <SelectCommon
                  control={control}
                  options={imageSubTypeOptions}
                  placeholder="Image Subtype"
                  allowClear
                  name="imageSubTypeId"
                  label="Image Subtype"
                />
              </>
            )}

            {columnClass !== ColumnClass.CoreRow && (
              <SelectCommon
                allowClear
                label="Suite"
                name="suiteId"
                placeholder="Select suite"
                control={control}
                isRequired={true}
                options={suiteOptions}
                onChange={(value) => {
                  const columnClass = watch("columnClass");
                  if (value && columnClass) {
                    loadAttributeOptions(columnClass as ColumnClass, value);
                  } else {
                    setAttributeOptions([]);
                  }
                }}
                showSearch
                filterOption={(input, option) =>
                  option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            )}
            {columnClass === ColumnClass.Geology && (
              <SelectCommon
                allowClear
                label="Field"
                name="fieldId"
                placeholder="Select field"
                control={control}
                options={attributeOptions}
                showSearch
                filterOption={(input, option) =>
                  option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            )}
            {columnClass === ColumnClass.Geophysics && (
              <SelectCommon
                allowClear
                label="Attribute"
                name="fieldId"
                placeholder="Select attribute"
                control={control}
                options={attributeOptions}
                filterOption={(input, option) =>
                  option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                showSearch
              />
            )}
            {columnClass === ColumnClass.Assay && (
              <>
                <SelectCommon
                  allowClear
                  label="Attribute"
                  name="fieldId"
                  placeholder="Select attribute"
                  control={control}
                  options={attributeOptions}
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().indexOf(input.toLowerCase()) >=
                    0
                  }
                  showSearch
                />
                <SelectCommon
                  allowClear
                  label="Number Range"
                  name="numberRangeId"
                  placeholder="Select number range"
                  control={control}
                  options={listNumberRange?.items?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  onChange={(value) => {
                    setKeywordNumberRange("");
                  }}
                  searchValue={keywordNumberRange}
                  filterOption={false}
                />
              </>
            )}

            <InputNumberCommon
              label="Column Width"
              name="width"
              placeholder="Type column width here"
              control={control}
              isRequired={true}
            />

            <ToogleCommon label="Is Active" name="isActive" control={control} />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={
                  loadingCreateLoggingViewColumn ||
                  loadingUpdateLoggingViewColumn
                }
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update logging view column"
                  : "Add logging view column"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
