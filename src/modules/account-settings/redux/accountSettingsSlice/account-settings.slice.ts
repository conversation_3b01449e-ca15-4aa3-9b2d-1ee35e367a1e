import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import { AccountSettingsEntity } from "../../model/entities/account-settings.entities";

const initialState: AccountSettingsSliceState = {
  detail: {},
};

export const accountSettingsSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    updateAccountSettings: (
      state,
      action: PayloadAction<AccountSettingsEntity>
    ) => {
      state.detail = action.payload;
    },
    clearAccountSettings: (state) => {
      state.detail = {};
    },
  },
  extraReducers: (builder) => {
    builder;
  },
});

export interface AccountSettingsSliceState {
  detail?: AccountSettingsEntity;
}

export const { updateAccountSettings, clearAccountSettings } =
  accountSettingsSlice.actions;
