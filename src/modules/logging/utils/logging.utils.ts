import { StandardType, Type } from "@/modules/image/model/enum/images.enum";
import { ColumnClass } from "@/modules/logging-view/enum/enum";
import { CONFIG } from "../constants/logging.constants";
import { EnumLoggingViewStack } from "../model/enum/logging.enum";
import { Coordinate } from "../types/logging.types";

import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { LoggingRowData } from "../components/logging-grid/table-geology-types";
import { SegmentationResult } from "../model/dtos/logging.config";

interface TransformedSegmentation extends SegmentationResult {
  intersections: [number, number][];
  points: number[][];
}

export const transformSegmentations = (
  segmentations: any[],
  type: EnumLoggingViewStack,
  imageData: { x: number; y: number; height: number },
  startY: number
): any[] => {
  if (type !== EnumLoggingViewStack.Image) return [];

  return segmentations.map((segmentation) => {
    const points = segmentation.points.map((point) => {
      return [point[0] - imageData.x, startY + point[1] - imageData.y] as [
        number,
        number
      ];
    });

    return {
      ...segmentation,
      points,
    };
  });
};

export const calculateCoordinate = (
  depthFrom: number,
  depthTo: number
): Coordinate => {
  const startY = depthFrom * CONFIG.SCALE;
  const endY = depthTo * CONFIG.SCALE;
  const height = endY - startY;
  return { height, startY, endY };
};

export const calculateStartX = (
  index: number,
  columns: any[],
  gap: number
): number => {
  let startX = 40;
  for (let i = 0; i < index; i++) {
    startX += columns[i].width + gap;
  }
  return startX;
};

export const processLoggingViewData = (
  loggingViewData: any,
  loggingViewColumn: any[],
  gap: number
) => {
  const processedData = loggingViewColumn.map((item) => {
    const fieldType = item?.geologySuiteField?.geologyField?.type;
    let data;
    let coreTypeName;
    console.log("loggingViewData: ", loggingViewData);

    switch (item?.columnClass) {
      case ColumnClass.Geology:
        data = (loggingViewData?.geology?.dataEntries ?? [])
          ?.map((dataEntry: any) => ({
            ...dataEntry,
            dataEntryValues: dataEntry.dataEntryValues.filter(
              (dataEntryValue: any) => dataEntryValue.fieldType === fieldType
            ),
          }))
          .filter((dataEntry: any) => {
            return dataEntry?.depthFrom < dataEntry?.depthTo;
          });
        break;
      case ColumnClass.Geophysics:
        data = loggingViewData?.geophysics;
        break;
      case ColumnClass.Assay:
        data = loggingViewData?.assay?.filter((assay: any) => {
          return (
            !isNaN(Number(assay?.["Depth From"])) &&
            !isNaN(Number(assay?.["Depth To"])) &&
            Number(assay?.["Depth From"]) < Number(assay?.["Depth To"])
          );
        });
        break;
      case ColumnClass.CoreRow:
        const firstCoreRow = loggingViewData?.coreRows?.[0];

        const coreRowData: any[] = firstCoreRow?.imageCrops ?? [];

        data = coreRowData
          ?.filter((rowData) => rowData?.type?.toLowerCase() === "row")
          ?.sort((a, b) => a?.depthFrom - b?.depthFrom)
          ?.map((rowData) => {
            const coordinate = JSON.parse(rowData?.coordinate ?? "{}");

            const viewHeight = rowData.depthTo - rowData.depthFrom;
            const viewWidth =
              (coordinate.Height / coordinate.Width) * viewHeight;
            return {
              ...rowData,
              height: viewHeight,
              width: viewWidth,
            };
          });

        break;
    }

    return {
      ...item,
      data,
      coreTypeName,
    };
  });

  return processedData.map((item, index) => ({
    ...item,
    startX: calculateStartX(index, processedData, gap),
  }));
};

// ============================================================================
// TABLE EDITABLE UTILITY FUNCTIONS
// ============================================================================

/**
 * Rounds depth values to 2 decimal places for consistent comparison
 */
export const roundDepth = (value: number): number => {
  return Math.round(value * 100) / 100;
};

/**
 * Determines if a field is compound (has multiple inputs)
 */
export const isCompoundField = (
  fieldIndex: number,
  targetRow: any
): boolean => {
  if (fieldIndex < 2) return false; // depthFrom and depthTo are single fields

  const geologySuiteFieldIndex = fieldIndex - 2;
  const dataEntryValue = targetRow.dataEntryValues?.[geologySuiteFieldIndex];
  const fieldType = dataEntryValue?.fieldType;

  // RockType (7) and RockSelect (8) fields are compound when they have number inputs
  return fieldType === FieldType.RockType || fieldType === FieldType.RockSelect;
};

/**
 * Gets the number of sub-fields in a compound field
 */
export const getSubFieldCount = (
  fieldIndex: number,
  targetRow: any
): number => {
  if (isCompoundField(fieldIndex, targetRow)) {
    return 2; // Rock select + number input
  }
  return 1; // Single input field
};

/**
 * Calculates dynamic column width based on field type
 */
export const getDynamicColumnWidth = (field: any): number => {
  if (
    field?.geologyField?.type === FieldType.RockType ||
    field?.geologyField?.type === FieldType.RockSelect
  ) {
    return 250;
  }

  if (field?.geologyField?.type === FieldType.RockTree) {
    return 200;
  }

  if (field?.geologyField?.type === FieldType.Description) {
    return 200;
  }

  return 150; // Default width
};

/**
 * Font size adjustment utility
 */
export const adjustFontSize = (
  currentSize: number,
  increment: boolean
): number => {
  const newSize = increment ? currentSize + 1 : currentSize - 1;
  return Math.max(10, Math.min(20, newSize));
};

/**
 * Filters rows based on search text
 */
export const createFilteredRows = (
  rows: LoggingRowData[],
  searchText: string
): LoggingRowData[] => {
  if (!searchText.trim()) {
    return rows;
  }

  return rows.filter((row) => {
    // Search in depth values
    if (
      row?.depthFrom?.toString().includes(searchText) ||
      row?.depthTo?.toString().includes(searchText)
    ) {
      return true;
    }

    // Search in field values
    return row.dataEntryValues.some((dataEntry) => {
      switch (dataEntry.fieldType) {
        case FieldType.Description:
          return dataEntry.description
            ?.toLowerCase()
            .includes(searchText.toLowerCase());
        case FieldType.NumberField:
          return dataEntry.numberValue?.toString().includes(searchText);
        case FieldType.DateField:
          return dataEntry.dateValue?.includes(searchText);
        default:
          return false;
      }
    });
  });
};

/**
 * Validation result interface
 */
export interface ValidationResult {
  errors: string[];
  fieldErrors: { [key: string]: string };
}

/**
 * Enhanced validation function with field-specific error tracking
 */
export const validateDepthConstraints = (
  rows: LoggingRowData[],
  geologySuiteFields?: any[]
): ValidationResult => {
  const errors: string[] = [];
  const fieldErrors: { [key: string]: string } = {};

  rows.forEach((row, index) => {
    // Ensure depth values are numbers and handle potential string inputs
    const rawDepthFrom = Number(row.depthFrom);
    const rawDepthTo = Number(row.depthTo);

    // Skip validation if depth values are invalid numbers
    if (isNaN(rawDepthFrom) || isNaN(rawDepthTo)) {
      const errorMsg = `Row ${
        index + 1
      }: Invalid depth values (must be numbers)`;
      errors.push(errorMsg);

      if (isNaN(rawDepthFrom)) {
        fieldErrors[`rows.${index}.depthFrom`] = "Must be a valid number";
      }
      if (isNaN(rawDepthTo)) {
        fieldErrors[`rows.${index}.depthTo`] = "Must be a valid number";
      }
      return;
    }

    // Round to 2 decimal places for consistent comparison
    const depthFrom = roundDepth(rawDepthFrom);
    const depthTo = roundDepth(rawDepthTo);

    // Check depthFrom < depthTo
    if (depthFrom >= depthTo) {
      const errorMsg = `Row ${index + 1}: DF must < depth to`;
      errors.push(errorMsg);

      fieldErrors[`rows.${index}.depthTo`] = `Must > ${depthFrom}`;
    }

    // Check for overlaps with other rows
    const overlappingRows: number[] = [];

    rows.forEach((otherRow, otherIndex) => {
      // Skip the current row
      if (otherIndex === index) {
        return;
      }

      const rawOtherDepthFrom = Number(otherRow.depthFrom);
      const rawOtherDepthTo = Number(otherRow.depthTo);

      // Skip if other row has invalid depth values
      if (isNaN(rawOtherDepthFrom) || isNaN(rawOtherDepthTo)) {
        return;
      }

      // Round to 2 decimal places for consistent comparison
      const otherDepthFrom = roundDepth(rawOtherDepthFrom);
      const otherDepthTo = roundDepth(rawOtherDepthTo);

      // Check for actual overlaps (not just adjacent ranges)
      // Two ranges overlap if: max(start1, start2) < min(end1, end2)
      const overlapStart = Math.max(depthFrom, otherDepthFrom);
      const overlapEnd = Math.min(depthTo, otherDepthTo);

      if (overlapStart < overlapEnd) {
        overlappingRows.push(otherIndex + 1); // +1 for 1-based indexing
      }
    });

    if (overlappingRows.length > 0) {
      const errorMsg = `Row ${
        index + 1
      }: Depth range (${depthFrom}-${depthTo}) overlaps with row(s) ${overlappingRows.join(
        ", "
      )}`;
      errors.push(errorMsg);

      fieldErrors[
        `rows.${index}.depthFrom`
      ] = `Overlaps with ${overlappingRows.join(", ")}`;
      fieldErrors[
        `rows.${index}.depthTo`
      ] = `Overlaps with ${overlappingRows.join(", ")}`;
    }

    // Validate mandatory geology suite fields
    if (geologySuiteFields) {
      row.dataEntryValues.forEach((dataEntryValue, dataEntryIndex) => {
        if (dataEntryValue.isMandatory) {
          let isEmpty = false;
          let fieldName = "";

          // Check if the field is empty based on field type
          switch (dataEntryValue.fieldType) {
            case FieldType.NumberField:
              isEmpty =
                dataEntryValue.numberValue === null ||
                dataEntryValue.numberValue === undefined;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.numberValue`;
              break;
            case FieldType.Description:
              isEmpty =
                !dataEntryValue.description ||
                dataEntryValue.description.trim() === "";
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.description`;
              break;
            case FieldType.DateField:
              isEmpty = !dataEntryValue.dateValue;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.dateValue`;
              break;
            case FieldType.Colour:
              isEmpty = !dataEntryValue.colourId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.colourId`;
              break;
            case FieldType.PickList:
              isEmpty = !dataEntryValue.pickListItemId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.pickListItemId`;
              break;
            case FieldType.RockType:
            case FieldType.RockSelect:
              // Combined validation: both rockTypeId and numberValue are required
              isEmpty =
                !dataEntryValue.rockTypeId || !dataEntryValue.numberValue;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.rockTypeId`;
              break;
            case FieldType.RockGroup:
              isEmpty = !dataEntryValue.rockTypeId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.rockTypeId`;
              break;
            case FieldType.RockTree:
              isEmpty = !dataEntryValue.rockNodeId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.rockNodeId`;
              break;
            default:
              isEmpty = !dataEntryValue.value;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.value`;
          }

          if (isEmpty) {
            const geologySuiteField = geologySuiteFields?.find(
              (field: any) => field.id === dataEntryValue.geologysuiteFieldId
            );
            const fieldDisplayName = geologySuiteField?.name || "Field";

            // Specific error messages for combined fields
            if (
              dataEntryValue.fieldType === FieldType.RockType ||
              dataEntryValue.fieldType === FieldType.RockSelect
            ) {
              const missingRock = !dataEntryValue.rockTypeId;
              const missingNumber = !dataEntryValue.numberValue;

              if (missingRock && missingNumber) {
                errors.push(
                  `Row ${
                    index + 1
                  }: ${fieldDisplayName} requires both rock selection and number value`
                );
                fieldErrors[fieldName] = "These fields are required";
              } else if (missingRock) {
                errors.push(
                  `Row ${
                    index + 1
                  }: ${fieldDisplayName} requires rock selection`
                );
                fieldErrors[fieldName] = "Rock selection is required";
              } else if (missingNumber) {
                errors.push(
                  `Row ${index + 1}: ${fieldDisplayName} requires number value`
                );
                fieldErrors[
                  `rows.${index}.dataEntryValues.${dataEntryIndex}.numberValue`
                ] = "Number value is required";
              }
            } else {
              errors.push(`Row ${index + 1}: ${fieldDisplayName} is required`);
              fieldErrors[fieldName] = "This field is required";
            }
          }
        }
      });
    }
  });

  return { errors, fieldErrors };
};
