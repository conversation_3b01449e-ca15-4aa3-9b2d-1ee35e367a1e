import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import downholeDataRequest from "@/modules/downhole-point/api/down-hole-data.api";
import imageRequest from "@/modules/image/api/image.api";
import { ImageQuery } from "@/modules/image/interface/image.interface";
import dataEntryRequest from "../../api/data-entry.api";
import { updateReviewDepthFrom } from "./logging.slice";
import { entries, isEmpty } from "lodash";

export const getGeologyData = createAppAsyncThunk(
  "geologySuite/getGeologyData",
  async (
    query: {
      geology: {
        GeologySuiteId?: any;
        DrillholeId?: number;
      };
      image?: {
        projectIds: number[];
        prospectIds: number[];
        holeIds: number[];
        skipCount: number;
        maxResultCount: number;
      };
    },
    { dispatch }
  ) => {
    const geologyDatas = await dataEntryRequest.getAllDataEntry({
      GeologySuiteId: query.geology.GeologySuiteId,
      DrillholeId: query.geology.DrillholeId,
      sortOrder: "DESC",
    });

    if (geologyDatas?.data?.items.length === 0) {
      const holeIds = isEmpty(query.image?.holeIds)
        ? [Number(query.geology.DrillholeId)]
        : query.image?.holeIds;

      const imageResponse = await imageRequest.getImages({
        ...query.image,
        projectIds: query?.image?.projectIds,
        prospectIds: query?.image?.prospectIds,
        holeIds: holeIds,
        skipCount: 0,
        maxResultCount: 1,
      });

      const reviewDepthFrom = imageResponse.data?.items[0]?.depthFrom ?? 0;
      dispatch(updateReviewDepthFrom(reviewDepthFrom));
    } else {
      let previousLoggingDepth = Math.max(
        ...(geologyDatas?.data?.items ?? [])
          .map((item) => item?.depthTo)
          .filter(Boolean)
      );
      if (previousLoggingDepth !== -Infinity) {
        dispatch(updateReviewDepthFrom(previousLoggingDepth));
      } else {
        dispatch(updateReviewDepthFrom(0));
      }
    }
    return geologyDatas.data;
  }
);

export const getExtraViewModeImages = createAppAsyncThunk(
  "geologySuite/getHyperspectureImage",
  async (query: ImageQuery) => {
    const response = await imageRequest.getImages(query);
    return response.data;
  }
);
export const getLoggingInfo = createAppAsyncThunk(
  "geologySuite/loggingInfos",
  async (query: {
    GeologySuiteId?: number;
    DrillholeId?: number;
    DepthFrom?: number;
    DepthTo?: number;
    skipCount?: number;
    maxResultCount?: number;
  }) => {
    const response = await dataEntryRequest.getAllDataEntry(query);
    return response.data;
  }
);

export const getLoggingInfoInfinite = createAppAsyncThunk(
  "geologySuite/loggingInfosInfinite",
  async (query: {
    GeologySuiteId?: number;
    DrillholeId?: number;
    DepthFrom?: number;
    DepthTo?: number;
    skipCount?: number;
    maxResultCount?: number;
    reset?: boolean;
  }) => {
    const response = await dataEntryRequest.getAllDataEntry({
      GeologySuiteId: query.GeologySuiteId,
      DrillholeId: query.DrillholeId,
      DepthFrom: query.DepthFrom,
      DepthTo: query.DepthTo,
      skipCount: query.skipCount ?? 0,
      maxResultCount: query.maxResultCount ?? 15,
      sortOrder: "DESC",
    });
    return {
      ...response.data,
      reset: query.reset ?? false,
    };
  }
);
