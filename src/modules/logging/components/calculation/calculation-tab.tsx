import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import { useGetListDhCalculation } from "@/modules/dh-calculation/hooks/useGetListDhCalculation";
import {
  DeleteOutlined,
  EditOutlined,
  ExportOutlined,
} from "@ant-design/icons";
import { Radio, RadioChangeEvent, TabsProps } from "antd";
import type { SortOrder } from "antd/es/table/interface";
import { useEffect, useState } from "react";
import recoveryRequest from "../../api/recovery.api";
import { updateRecoveries, updateTrayDepths } from "../../redux/loggingSlice";
import { RenderRqlCalculation } from "../render-rql-calculation";
import { CalculationModal } from "./calculation-modal";

export function CalculationTab({
  setModalExport,
}: {
  setModalExport?: (value: boolean) => void;
}) {
  const recoveries = useAppSelector((state) => state.logging.recoveries);
  const trayDepths = useAppSelector((state) => state.logging.trayDepths);
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });
  const dispatch = useAppDispatch();
  const unitSymbol = useAppSelector(
    (state) => state.accountSettings.detail?.unitsSymbol
  );
  const columns = [
    {
      width: 100,
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },

    {
      width: 200,
      title: "Depth From",
      dataIndex: "ocrValueFrom",
      key: "ocrValueFrom",
      sorter: {
        multiple: 1,
        compare: (a, b) => Number(a.ocrValueFrom) - Number(b.ocrValueFrom),
      },
      render: (value) => value.toFixed(2),
    },
    {
      width: 200,
      title: "Depth To",
      dataIndex: "ocrValueTo",
      key: "ocrValueTo",
      sorter: {
        multiple: 2,
        compare: (a, b) => Number(a.ocrValueTo) - Number(b.ocrValueTo),
      },
      render: (value) => value.toFixed(2),
    },
    {
      title: "Recovery (%)",
      dataIndex: "recovery",
      render: (value) => value.toFixed(2),
    },
    {
      title: `Drilled Length ${unitSymbol ? `(${unitSymbol})` : ""}`,
      dataIndex: "recovery",
      render: (value, record) => {
        return (
          <div>{(record.ocrValueTo - record.ocrValueFrom).toFixed(2)}</div>
        );
      },
    },
    {
      title: `Core Length ${unitSymbol ? `(${unitSymbol})` : ""}`,
      dataIndex: "length",
      render: (value, record) => {
        return <div>{record.length.toFixed(2)}</div>;
      },
    },
  ];

  const trayDepthColumns = [
    {
      width: 50,
      title: "Tray Number",
      dataIndex: "trayNumber",
      key: "trayNumber",
      defaultSortOrder: "ascend" as SortOrder,
      sorter: {
        multiple: 1,
        compare: (a, b) => Number(a.trayNumber) - Number(b.trayNumber),
      },
    },
    {
      width: 150,
      title: "Start Depth",
      dataIndex: "startDepth",
      key: "startDepth",
      sorter: {
        multiple: 2,
        compare: (a, b) => Number(a.startDepth) - Number(b.startDepth),
      },
      render: (value) => value.toFixed(2),
    },
    {
      width: 150,
      title: "End Depth",
      dataIndex: "endDepth",
      key: "endDepth",
      sorter: {
        multiple: 3,
        compare: (a, b) => Number(a.endDepth) - Number(b.endDepth),
      },
      render: (value) => value.toFixed(2),
    },
    // {
    //   title: `Length ${unitSymbol ? `(${unitSymbol})` : ""}`,
    //   dataIndex: "length",
    //   render: (_, record) => {
    //     return <div>{(record.endDepth - record.startDepth).toFixed(2)}</div>;
    //   },
    // },
  ];

  const fetchRecoveries = async () => {
    const result = await recoveryRequest.getRecoveryByDrillHole({
      drillHoleId: Number(selectedDrillhole?.value),
    });
    if (result.state === RequestState.success && result.data?.items) {
      dispatch(
        updateRecoveries(
          (result.data.items ?? []).sort(
            (a, b) => a.ocrValueFrom - b.ocrValueFrom
          )
        )
      );
    }
  };

  const fetchTrayDepths = async () => {
    const result = await recoveryRequest.getTrayDepthResult({
      drillHoleId: Number(selectedDrillhole?.value),
      maxResultCount: 1000,
      skipCount: 0,
    });
    if (result.state === RequestState.success && result.data?.items) {
      dispatch(updateTrayDepths(result.data.items));
    }
  };

  useEffect(() => {
    fetchRecoveries();
    fetchTrayDepths();
  }, [selectedDrillhole]);

  const [items, setItems] = useState<TabsProps["items"]>([
    {
      key: "1",
      label: "Recovery",
      children: (
        <TableCommon
          key={JSON.stringify(recoveries)}
          columns={columns}
          dataSource={recoveries}
        />
      ),
    },
    {
      key: "2",
      label: "Tray Depths Recalc",
      children: (
        <TableCommon
          key={JSON.stringify(trayDepths)}
          columns={trayDepthColumns}
          dataSource={trayDepths}
          // scroll={{ x: "max-content", y: 6 * 110 }}
        />
      ),
    },
  ]);
  const globalProjectId = useAppSelector(
    (state) => state.user.userInfo.projectId
  );
  const { request: requestDhCalculation } = useGetListDhCalculation();
  useEffect(() => {
    requestDhCalculation(
      {
        maxResultCount: 1000,
        skipCount: 0,
        projectId: globalProjectId,
      },
      (res) => {
        const data = res?.items ?? [];
        // Create a new array with the existing tabs and add new ones
        const newItems = [
          {
            key: "1",
            label: "Recovery",
            children: (
              <TableCommon
                key={JSON.stringify(recoveries)}
                columns={columns}
                dataSource={[...(recoveries ?? [])]}
                pagination={{
                  total: recoveries.length,
                  pageSize: 50,
                }}
                // scroll={{ x: "max-content", y: 6 * 110 }}
              />
            ),
          },
          {
            key: "2",
            label: "Tray Depths Recalc",
            children: (
              <TableCommon
                key={JSON.stringify(trayDepths)}
                columns={trayDepthColumns}
                dataSource={trayDepths}
                // scroll={{ x: "max-content", y: 6 * 110 }}
                pagination={{
                  total: trayDepths.length,
                  pageSize: 50,
                }}
              />
            ),
          },
        ];

        // Add the dynamic tabs from the API
        data.forEach((item, index) => {
          newItems.push({
            key: item.name + String(index),
            label: item.name,
            children: <RenderRqlCalculation item={item} />,
          });
        });

        setItems(newItems);
      }
    );
  }, [globalProjectId, trayDepths, recoveries]);

  const [mode, setMode] = useState<any>("1");

  const handleModeChange = (e: RadioChangeEvent) => {
    setMode(e.target.value);
  };
  return (
    <div>
      <CalculationModal modalState={modalState} setModalState={setModalState} />
      <div className="flex gap-3">
        <Radio.Group
          onChange={handleModeChange}
          value={mode}
          style={{ marginBottom: 8 }}
          key={JSON.stringify(recoveries) + JSON.stringify(items?.length)}
        >
          {items?.map((item) => {
            return (
              <Radio.Button key={item.key} value={item.key}>
                {item.label}
              </Radio.Button>
            );
          })}
        </Radio.Group>
        <div className="">
          <button
            onClick={() => {
              setModalExport?.(true);
            }}
            className="flex items-center gap-2 p-1 bg-[#0F763D] text-white rounded-md hover:bg-[#0F763D]/80"
          >
            <ExportOutlined />
            Export
          </button>
        </div>
      </div>

      {items?.find((item) => item.key === mode)?.children}
    </div>
  );
}
