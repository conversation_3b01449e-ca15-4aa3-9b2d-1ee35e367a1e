import React, { memo, useMemo, useCallback } from "react";
import { Control } from "react-hook-form";
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { FieldDepth } from "./index";
import { FieldRenderer } from "./field-renderer";

// Types
interface LoggingFormData {
  rows: LoggingRowData[];
}

interface LoggingRowData {
  id: string;
  valueId: string;
  depthFrom?: number;
  depthTo?: number;
  rowStatus: any;
  geologySuiteId: number;
  drillholeId: number;
  loading?: boolean;
  errorDetail?: any;
  dataEntryValues: DataEntryValueData[];
}

interface DataEntryValueData {
  fieldType: FieldType;
  geologysuiteFieldId: string;
  isMandatory: boolean;
  valueId: string;
  value: any;
  numberId?: number;
  numberValue?: number;
  pickListItemId?: number;
  rockTypeId?: number;
  colourId?: number;
  dateValue?: string;
  description?: string;
  number?: { id?: number; unit?: { code?: string } };
  rockNodeId?: number;
}

// Memoized EditableCell component
interface EditableCellProps {
  control: Control<LoggingFormData>;
  rowIndex: number;
  // For depthFrom/depthTo, this will be 'depthFrom' or 'depthTo'.
  // For dynamic fields, this will be geologySuiteField.id
  fieldIdentifier: string;
  // Provided for dynamic fields, null for depthFrom/depthTo
  geologySuiteField?: any; // Consider a more specific type if available for geologySuiteField
  // Provided for dynamic fields, null for depthFrom/depthTo
  dataEntryValue?: DataEntryValueData;
  recordLoading?: boolean;
  onCellKeyDown: (
    event: React.KeyboardEvent,
    rowIndex: number,
    fieldIndexForNav: number // 0 for depthFrom, 1 for depthTo, 2+ for dynamic
  ) => void;
  onCellFocus?: (rowIndex: number, fieldIndexForNav: number) => void;
  onCellBlur?: (rowIndex: number, fieldIndexForNav: number) => void;
  fieldIndexForNav: number; // The actual index for navigation logic
  geologyFieldType?: number;
  // Pass down the master lists for options
  allColours?: any[];
  allRockTypes?: any[];
  idPrefix: string; // To ensure unique IDs for inputs e.g. record.id or record.valueId
  dataEntryValueIndex?: number; // Index of dataEntryValue in the parent row's dataEntryValues array
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void; // New prop for row status updates
}

export const EditableCell = memo<EditableCellProps>(
  ({
    geologyFieldType = -1,
    control,
    rowIndex,
    fieldIdentifier,
    geologySuiteField,
    dataEntryValue,
    dataEntryValueIndex, // New prop
    recordLoading,
    onCellKeyDown,
    onCellFocus,
    onCellBlur,
    fieldIndexForNav,
    allColours,
    allRockTypes,
    idPrefix,
    onFieldChange,
  }) => {
    const fieldType = useMemo(() => {
      if (fieldIdentifier === "depthFrom" || fieldIdentifier === "depthTo") {
        // Special case for depth fields, not a standard FieldType enum
        return "DEPTH" as any; // Or handle appropriately if FieldType includes DEPTH
      }
      return geologyFieldType;
    }, [fieldIdentifier, geologyFieldType]);

    const fieldName = useMemo(() => {
      if (fieldIdentifier === "depthFrom") return `rows.${rowIndex}.depthFrom`;
      if (fieldIdentifier === "depthTo") return `rows.${rowIndex}.depthTo`;

      if (
        geologySuiteField &&
        dataEntryValue &&
        typeof dataEntryValueIndex === "number" &&
        dataEntryValueIndex !== -1
      ) {
        const devIndex = dataEntryValueIndex; // Use the passed index

        switch (geologyFieldType) {
          case FieldType.Colour:
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.colourId`;
          case FieldType.NumberField:
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.numberValue`;
          case FieldType.Description:
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.description`;
          case FieldType.PickList:
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.pickListItemId`;
          case FieldType.DateField:
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.dateValue`;
          case FieldType.RockType:
          case FieldType.RockSelect:
          case FieldType.RockGroup:
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.rockTypeId`;
          case FieldType.RockTree:
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.rockNodeId`;
          default:
            // This case should ideally not be hit if fieldType is always one of the above for dynamic fields
            return `rows.${rowIndex}.dataEntryValues.${devIndex}.value`;
        }
      }
      // Fallback or error condition if required information is missing
      return `rows.${rowIndex}.INVALID_FIELD_SETUP_MISSING_INDEX`;
    }, [rowIndex, fieldIdentifier, geologyFieldType, dataEntryValueIndex]);

    const fieldConfig = useMemo(() => {
      if (
        fieldIdentifier === "depthFrom" ||
        fieldIdentifier === "depthTo" ||
        !geologySuiteField ||
        !dataEntryValue
      ) {
        return {
          isMandatory: false, // Depth fields might have their own logic
          valueId: `${idPrefix}-${fieldIdentifier}`, // Unique ID for depth fields
          unit: undefined,
          options: [],
          treeData: [],
        };
      }

      let options: any[] = [];
      let treeData: any[] = [];

      switch (geologyFieldType) {
        case FieldType.Colour:
          options =
            allColours?.map((colour: any) => ({
              id: colour.id,
              name: colour.name,
              hexCode: colour.hexCode,
            })) || [];
          break;
        case FieldType.RockGroup:
          options =
            geologySuiteField?.geologyField?.rockGroup?.rockTypes?.map(
              (rockType: any) => ({
                id: rockType.id,
                name: rockType.name,
                code: rockType.code,
              })
            ) || [];
          break;
        case FieldType.RockType:
        case FieldType.RockSelect:
          options =
            allRockTypes?.map((rockType: any) => ({
              id: rockType.id,
              name: rockType.name,
              code: rockType.code,
              rockStyle: rockType.rockStyle,
            })) || [];
          break;
        case FieldType.PickList:
          options =
            geologySuiteField?.geologyField?.pickList?.pickListItems?.map(
              (item: any) => ({
                id: item.id,
                name: item.name,
              })
            ) || [];
          break;
        case FieldType.RockTree:
          treeData = geologySuiteField?.geologyField?.treeNode
            ? [geologySuiteField?.geologyField?.treeNode]
            : [];
          break;
      }

      return {
        isMandatory: geologySuiteField.isMandatory,
        // Use the valueId from dataEntryValue for dynamic fields,
        // and construct one for depth fields.
        valueId: dataEntryValue.valueId || `${idPrefix}-${fieldIdentifier}`,
        unit: dataEntryValue.number?.unit?.code,
        options,
        treeData,
      };
    }, [
      fieldIdentifier,
      geologySuiteField,
      dataEntryValue,
      allColours,
      allRockTypes,
      idPrefix,
    ]);

    const handleCellKeyDownInternal = useCallback(
      (event: React.KeyboardEvent) => {
        onCellKeyDown(event, rowIndex, fieldIndexForNav);
      },
      [onCellKeyDown, rowIndex, fieldIndexForNav]
    );

    const handleCellFocusInternal = useCallback(() => {
      onCellFocus?.(rowIndex, fieldIndexForNav);
    }, [onCellFocus, rowIndex, fieldIndexForNav]);

    const handleCellBlurInternal = useCallback(() => {
      onCellBlur?.(rowIndex, fieldIndexForNav);
    }, [onCellBlur, rowIndex, fieldIndexForNav]);

    if (fieldIdentifier === "depthFrom" || fieldIdentifier === "depthTo") {
      return (
        <FieldDepth
          control={control}
          name={fieldName}
          disabled={recordLoading}
          onKeyDown={handleCellKeyDownInternal}
          onFocus={handleCellFocusInternal}
          onBlur={handleCellBlurInternal}
          id={`${idPrefix}-${fieldIdentifier}`}
          onFieldChange={onFieldChange}
          rowIndex={rowIndex}
          fieldPath={fieldName}
        />
      );
    }

    if (
      !fieldType ||
      fieldName.includes("INVALID_FIELD_IDENTIFIER_SETUP") ||
      fieldName.includes("INVALID_INDEX")
    ) {
      // This can happen if dataEntryValue is not yet populated for a new row's dynamic field
      // or if fieldName construction failed.
      return (
        <div style={{ color: "red", fontSize: "10px", padding: "2px" }}>
          Cell Error
        </div>
      ); // Or a placeholder
    }

    return (
      <FieldRenderer
        control={control}
        fieldType={fieldType}
        fieldName={fieldName}
        fieldConfig={fieldConfig}
        disabled={recordLoading}
        onKeyDown={handleCellKeyDownInternal}
        onFocus={handleCellFocusInternal}
        onBlur={handleCellBlurInternal}
        onFieldChange={onFieldChange}
        rowIndex={rowIndex}
        fieldPath={fieldName}
      />
    );
  }
);

EditableCell.displayName = "EditableCell";
