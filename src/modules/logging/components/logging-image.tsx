import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { useGetListAttributeBySuiteId } from "@/modules/downhole-point/hooks/useGetAttributeBySuiteId";
import { useGetDownholeByProject } from "@/modules/downhole-point/hooks/useGetDownholeByProject";
import { useGetListAttributeByProjectId } from "@/modules/downhole-point/hooks/useGetListAttributeByProjectId";
import { getDetailDrillhole } from "@/modules/drillhole/redux/drillholeSlice/thunks";
import { getGeologySuite } from "@/modules/geology-suite/redux/thunks";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import imageRequest from "@/modules/image/api/image.api";
import { resizeLoggingPointData } from "@/modules/image/helpers/image.helpers";
import { ModalOcrList } from "@/modules/process-images/components/ocr/modal-ocr-list";
import { StructureSelectorType } from "@/modules/structure-type/enum/enum";
import { Button, Select, Tag, TreeSelect } from "antd";
import { isEmpty, uniqueId } from "lodash";
import { useSearchParams } from "next/navigation";
import { Fragment, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import recoveryRequest from "../api/recovery.api";
import { LoggingViewStack } from "../model/dtos/logging.config";
import {
  EnumLoggingExtraViews,
  EnumLoggingViewStack,
} from "../model/enum/logging.enum";
import {
  setImageHyperRows,
  updateExtraViews,
  updateMeasurePointsInterval,
  updateRecoveries,
  updateSelectedDrilhole,
  updateSelectedRockLineType,
} from "../redux/loggingSlice";
import { getExtraViewModeImages } from "../redux/loggingSlice/thunks";
import { OCRResultItem, RockLineType } from "../types/logging.types";
import { LoggingStage } from "./logging-stage";

const viewsOptions = [
  {
    label: "Below",
    value: EnumLoggingExtraViews.Below,
  },
  {
    label: "Overlay",
    value: EnumLoggingExtraViews.Overlay,
  },
];

const rockLineOptions = [
  {
    label: "Recovery",
    value: RockLineType.Recovery,
  },
  {
    label: "RQD",
    value: RockLineType.RQD,
  },
];

function LoggingImage({
  imageRows,
  viewModes,
  setViewModes,
  directOCRdata,
  setDirectOCRdata,
  image,
  directOCRdataRaw,
  getCurrentImage,
  handleDrillholeChange,
  setCurrentImage,
  selectedImageType,
  selectedImageSubtype,
}) {
  const dispatch = useAppDispatch();
  const queries: any = {};
  const searchParams = useSearchParams();

  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const params = new URLSearchParams(queries);

  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );

  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const structure = useAppSelector((state) => state.structure?.detail);

  const imageDepthFrom = useAppSelector((state) => state.logging.depthFrom);
  const imageDepthTo = useAppSelector((state) => state.logging.depthTo);
  const projectDetail = useAppSelector((state) => state.project.detail);
  const skipCount = useAppSelector((state) => state.logging.skipCount);

  const selectedRockLineType = useAppSelector(
    (state) => state.logging.selectedRockLineType
  );
  const loggingSuiteMode = useAppSelector(
    (state) => state.logging.loggingSuiteMode
  );
  const measurePointsInterval = useAppSelector(
    (state) => state.logging.measurePointsInterval
  );
  const imageExtraRows = useAppSelector(
    (state) => state?.logging?.imageExtraRows
  );
  const imageGap = useAppSelector((state) => state.logging.imageGap);

  const extraViews = useAppSelector((state) => state?.logging?.extraViews);
  const selectedDrillHole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const isShowSegmentation = useAppSelector(
    (state) => state.logging.isShowSegmentation
  );
  const isInterval = structure?.selector === StructureSelectorType.Interval;

  const [selectedAttribute, setSelectedAttribute] = useState<string[]>();
  const [selectedGeophysic, setSelectedGeophysic] = useState<string[]>();
  const [isOpenModalOCRList, setIsOpenModalOCRList] = useState(false);
  const [OCRdata, setOCRdata] = useState<OCRResultItem[]>([]);
  const [imageTypeValue, setImageTypeValue] = useState<string | undefined>();

  const { data: imageTypeData, isLoading: isFetchingImageType } =
    useQueryImageType();

  const transformDataToTreeNodes = (data: any[] | undefined) => {
    if (!data) return [];
    return data.map((type: any) => ({
      title: type.name,
      value: `type-${type.id}`,
      key: `type-${type.id}`,
      children: (type.imageSubtypes || []).map((subtype: any) => ({
        title: subtype.name,
        value: `subtype-${subtype.id}-type-${type.id}`,
        key: `subtype-${subtype.id}-type-${type.id}`,
      })),
    }));
  };

  const imageTypeTreeData = useMemo(
    () => transformDataToTreeNodes(imageTypeData?.data?.items),
    [imageTypeData]
  );

  const convertToTypeSubTypeId = (treeNodeValue: string) => {
    let typeId: number | undefined = undefined;
    let subTypeId: number | undefined = undefined;
    if (treeNodeValue) {
      if (treeNodeValue.startsWith("subtype-")) {
        const parts = treeNodeValue.split("-");
        subTypeId = Number(parts[1]);
        typeId = Number(parts[3]);
      } else if (treeNodeValue.startsWith("type-")) {
        const parts = treeNodeValue.split("-");
        typeId = Number(parts[1]);
      }
    }
    return {
      typeId,
      subTypeId,
    };
  };

  const handleImageTypeChange = (value: string) => {
    setImageTypeValue(value);
    const { typeId, subTypeId } = convertToTypeSubTypeId(value);

    if (
      (value && viewModes.includes(EnumLoggingExtraViews.Below)) ||
      viewModes.includes(EnumLoggingExtraViews.Overlay)
    ) {
      dispatch(
        getExtraViewModeImages({
          imageSubtypeId: subTypeId,
          imageTypeId: typeId,
          projectIds: [globalProjectId],
          prospectIds: [globalProspectId],
          holeIds: [selectedDrillhole?.value],
          depthFrom: imageDepthFrom,
          depthTo: parseFloat(imageDepthTo.toFixed(2)),
        })
      );
      dispatch(updateExtraViews(viewModes as EnumLoggingExtraViews[]));
    } else {
      dispatch(updateExtraViews([]));
      dispatch(setImageHyperRows([]));
    }
  };

  const onChangeViewModes = (selections) => {
    setViewModes(selections);

    if (!imageTypeValue) return;

    if (
      !imageTypeValue ||
      (!selections.includes(EnumLoggingExtraViews.Below) &&
        !selections.includes(EnumLoggingExtraViews.Overlay))
    ) {
      dispatch(updateExtraViews([]));
      dispatch(setImageHyperRows([]));
      return;
    }

    const { typeId, subTypeId } = convertToTypeSubTypeId(imageTypeValue);

    dispatch(
      getExtraViewModeImages({
        imageSubtypeId: subTypeId,
        imageTypeId: typeId,
        projectIds: [globalProjectId],
        prospectIds: [globalProspectId],
        holeIds: [selectedDrillhole?.value],
        depthFrom: imageDepthFrom,
        depthTo: parseFloat(imageDepthTo.toFixed(2)),
      })
    );
    dispatch(updateExtraViews(selections as EnumLoggingExtraViews[]));
  };

  const drillholeIdParams = params.get("drillholeId");

  const {
    data: geophysicsAttributes,
    request: requestGetListAttributeBySuiteId,
  } = useGetListAttributeBySuiteId();

  const handleSelectAttributes = (value) => {
    setSelectedAttribute(value);
  };

  const handleRockLineTypeChange = (value) => {
    dispatch(updateSelectedRockLineType(value));
  };

  const handleSelectGeophysics = (value) => {
    setSelectedGeophysic(value);
    if (value) {
      requestGetListAttributeBySuiteId({
        Id: value,
      });
    } else {
      setSelectedAttribute(undefined);
    }
  };

  const { request: requestGetDownholeByProject, data: dataDownholes } =
    useGetDownholeByProject();

  const onEnterChangeText = (data: any, value: string) => {
    const dataUpdate = directOCRdata.map((item) => {
      if (item.id === data.id) {
        return {
          ...item,
          text: value,
        };
      }
      return item;
    });
    setDirectOCRdata(dataUpdate);
    imageRequest
      .updateResultOCR({
        id: image?.id,
        ocr: JSON.stringify(dataUpdate),
      })
      .then(() => {
        toast.success("Update OCR success", {
          position: "top-center",
        });
      })
      .catch((err) => {
        toast.error("Update OCR failed", {
          position: "top-center",
        });
      });
  };

  const onDeleteOcr = (id: string) => {
    const dataUpdate = directOCRdata.filter((item) => item.id !== id);
    imageRequest
      .updateResultOCR({
        id: image?.id,
        ocr: JSON.stringify(dataUpdate),
      })
      .then(() => {
        setDirectOCRdata(dataUpdate);
        toast.success("Delete OCR success", {
          position: "top-center",
        });
      })
      .catch((err) => {
        toast.error("Delete OCR failed", {
          position: "top-center",
        });
      });
  };

  const onDblClickText = (data: any) => {
    setDirectOCRdata((prev) => {
      return prev.map((item) => {
        if (item.id === data.id) {
          return {
            ...item,
            isEdit: true,
          };
        }
        return {
          ...item,
          isEdit: false,
        };
      });
    });
  };

  const onChange = (newAttrs: any) => {
    const newAnnotations = directOCRdata.map((annotation: any) => {
      if (annotation.id === newAttrs.id) {
        return newAttrs;
      }
      return annotation;
    });
    setDirectOCRdata(newAnnotations);
  };

  const onChangeText = (data: any, value: string) => {
    setDirectOCRdata((prev) => {
      return prev.map((item) => {
        if (item.id === data.id) {
          return {
            ...item,
            draftText: Number(value),
          };
        }
        return item;
      });
    });
  };

  const refreshImageData = () => {
    const drillholeId = drillholeIdParams ?? selectedDrillHole?.value;
    const imageSkipCount = skipCount ?? 0;
    if (drillholeId) {
      dispatch(
        updateSelectedDrilhole({
          value: selectedDrillhole?.value,
          label: selectedDrillhole?.label,
        })
      );
      getCurrentImage(
        drillholeId,
        imageSkipCount + 1,
        selectedImageType,
        selectedImageSubtype
      );
      setCurrentImage(imageSkipCount + 1);
      dispatch(getDetailDrillhole(Number(drillholeId)))
        .unwrap()
        .then((res) => {
          handleDrillholeChange(res?.data?.id, {
            label: res?.data?.name,
            value: res?.data?.id,
          });
        });
    }
  };

  const loggingViewStacks = useMemo(() => {
    let stacks: any[] = [];
    let previousStartY = 200;
    let previousHeight = 0;
    for (let i = 0; i < imageRows.length; i++) {
      // Add image stacks
      let relativeStartX = 0;
      if (i === 0) {
        relativeStartX = 0;
      } else {
        for (let j = 0; j < i; j++) {
          relativeStartX = relativeStartX + imageRows[j].coordinate.Width;
        }
      }

      let imageStack: LoggingViewStack = {
        id: `${imageRows[i].id}-${EnumLoggingViewStack.Image}`,
        data: {
          src: imageRows[i].urlCroppedImage,
          width: imageRows[i].coordinate.Width,
          height: imageRows[i].coordinate.Height,
          depthFrom: imageRows[i].depthFrom,
          depthTo: imageRows[i].depthTo,
          isShowOCR: true,
          isShowText: true,
          id: imageRows[i].id,
          x: imageRows[i].coordinate.X,
          y: imageRows[i].coordinate.Y,
          relativeStartX: relativeStartX,
          relativeEndX: relativeStartX + imageRows[i].coordinate.Width,
          rockLines: imageRows[i].rockLines,
        },
        startY: previousStartY + previousHeight + imageGap,
        type: EnumLoggingViewStack.Image,
        index: i,
      };

      previousStartY = imageStack.startY;
      previousHeight = imageStack.data.height;

      stacks.push(imageStack);

      // // Add hyperspecture stack overlay
      if (
        imageExtraRows?.length > 0 &&
        !isEmpty(imageExtraRows?.[i]) &&
        extraViews.includes(EnumLoggingExtraViews.Overlay)
      ) {
        let hyperImageStack: LoggingViewStack = {
          id: `${imageExtraRows[i].id}-${EnumLoggingViewStack.Image}-${EnumLoggingExtraViews.Overlay}`,
          data: {
            src: imageExtraRows[i].urlCroppedImage,
            width: imageRows[i].coordinate.Width,
            height:
              imageExtraRows[i].coordinate.Height *
              (imageRows[i].coordinate.Width /
                imageExtraRows[i].coordinate.Width),
            depthFrom: imageExtraRows[i].depthFrom,
            depthTo: imageExtraRows[i].depthTo,
            isShowOCR: false,
            isShowText: false,
            id: imageRows[i].id,
          },
          startY: previousStartY,
          type: EnumLoggingViewStack.Overlay,
          index: i,
          uiProps: {
            opacity: 0.6,
          },
        };
        previousStartY = hyperImageStack.startY;
        previousHeight = hyperImageStack.data.height;

        stacks.push(hyperImageStack);
      }

      // Add hyperspecture stack below
      if (
        imageExtraRows?.length > 0 &&
        !isEmpty(imageExtraRows?.[i]) &&
        extraViews.includes(EnumLoggingExtraViews.Below)
      ) {
        let hyperImageStack: LoggingViewStack = {
          id: `${imageExtraRows[i].id}-${EnumLoggingViewStack.Image}-${EnumLoggingExtraViews.Below}`,
          data: {
            src: imageExtraRows[i].urlCroppedImage,
            width: imageRows[i].coordinate.Width,
            height:
              imageExtraRows[i].coordinate.Height *
              (imageRows[i].coordinate.Width /
                imageExtraRows[i].coordinate.Width),
            depthFrom: imageExtraRows[i].depthFrom,
            depthTo: imageExtraRows[i].depthTo,
            isShowOCR: false,
            isShowText: false,
            id: imageRows[i].id,
          },
          startY: previousStartY + previousHeight,
          type: EnumLoggingViewStack.Below,
          index: i,
        };

        previousStartY = hyperImageStack.startY;
        previousHeight = hyperImageStack.data.height;

        stacks.push(hyperImageStack);
      }

      // Add point data stacks
      let pointStacks: LoggingViewStack[] = [];
      if (selectedAttribute?.length !== 0) {
        pointStacks = (selectedAttribute ?? [])
          .map((geophysicName, index) => {
            const geophsicDatas = dataDownholes
              ?.filter(
                (dataDownhole) =>
                  Number(dataDownhole["Depth (m)"]) >=
                    Number(imageRows[i].depthFrom) &&
                  Number(dataDownhole["Depth (m)"]) <=
                    Number(imageRows[i].depthTo)
              )
              .map((dataDownhole) => ({
                x: dataDownhole["Depth (m)"],
                y: dataDownhole[geophysicName],
              }));

            // Filter to get unique data, always get latest value
            const uniqueGeophysicData = [
              ...new Map(geophsicDatas.map((item) => [item.x, item])).values(),
            ];
            uniqueGeophysicData.sort(
              (a, b) => parseFloat(a.x) - parseFloat(b.x)
            );
            if (uniqueGeophysicData.length === 0) return null;

            const resizePoints = resizeLoggingPointData(
              uniqueGeophysicData,
              imageRows[i].depthFrom,
              imageRows[i].depthTo,
              imageRows[i].coordinate.Width,
              120
            );
            const stack: LoggingViewStack = {
              id: uniqueId(),
              type: EnumLoggingViewStack.Point,
              data: resizePoints,
              index: index,
              startY: previousStartY + previousHeight + 300,
            };

            previousStartY = stack.startY;
            previousHeight = 120;
            return stack;
          })
          .filter((item) => item !== null);
      }
      stacks.push(...pointStacks);
    }

    return stacks;
  }, [
    imageRows,
    dataDownholes,
    selectedAttribute,
    imageExtraRows,
    extraViews,
    imageGap,
  ]);

  const fetchRecoveries = async () => {
    if (isShowSegmentation && selectedDrillhole?.value) {
      try {
        const result = await recoveryRequest.getRecoveryByDrillHole({
          drillHoleId: Number(selectedDrillhole.value),
        });
        if (result.state === RequestState.success && result.data?.items) {
          dispatch(updateRecoveries(result.data.items));
        }
      } catch (error) {
        toast.error("Failed to fetch recoveries");
      }
    }
  };
  useEffect(() => {
    fetchRecoveries();
  }, [isShowSegmentation, selectedDrillhole]);

  useEffect(() => {
    if (globalProjectId && selectedDrillhole?.label) {
      requestGetDownholeByProject({
        DepthFrom: image?.depthFrom,
        DepthTo: image?.depthTo,
        DrillHoleName: [selectedDrillhole?.label],
        projectId: globalProjectId,
      });

      dispatch(getGeologySuite({ isActive: true, maxResultCount: 1000 }));
    }
  }, [globalProjectId, selectedDrillhole?.label]);

  return isEmpty(imageRows) ? (
    <div className="flex flex-col items-center justify-center mt-2 w-full ">
      <div className="flex flex-col md:items-center justify-center max-w-md p-8 bg-white rounded-lg shadow-md">
        <img
          src="/images/ic_notResult.png"
          alt="No cropped images"
          className="md:w-40 md:h-40 w-20 h-20 mb-6 opacity-80"
        />
        <h2 className="text-2xl font-bold text-gray-700 mb-2">
          No cropped images available
        </h2>
        <p className="text-gray-500 text-center mb-6">
          There are no cropped images available.
        </p>
        <div className="flex items-center justify-center">
          <div className="w-3 h-3 bg-amber-500 rounded-full animate-ping mr-2"></div>
          <span className="text-amber-500 font-medium">
            Try selecting a different drill hole
          </span>
        </div>
      </div>
    </div>
  ) : (
    <Fragment>
      <div className="">
        <div className="flex flex-col gap-1 mt-1 md:relative">
          <div className="flex items-center justify-between gap-8 w-full pb-1">
            <div className="flex gap-3 items-center">
              <div className="flex items-center gap-3">
                <div className="items-center flex col-span-2">
                  <p className="font-medium col-span-1 min-w-12 w-12">Image</p>
                  <TreeSelect
                    className="min-w-40 col-span-1"
                    placeholder="Select image type"
                    treeData={imageTypeTreeData}
                    value={imageTypeValue}
                    onChange={handleImageTypeChange}
                    loading={isFetchingImageType}
                    allowClear
                    treeDefaultExpandAll
                    dropdownStyle={{
                      maxHeight: 400,
                      overflow: "auto",
                      minWidth: "min(300px, 90vw)", // Responsive: 300px on desktop, 90% viewport width on mobile
                      maxWidth: "min(600px, 95vw)", // Responsive: 600px on desktop, 95% viewport width on mobile
                    }}
                  />
                  {imageTypeValue && (
                    <Select
                      mode="multiple"
                      className="min-w-40 col-span-1"
                      placeholder="View mode"
                      options={viewsOptions}
                      value={viewModes}
                      onChange={onChangeViewModes}
                    />
                  )}
                </div>
                <div className="items-center flex">
                  <p className="font-medium col-span-1">Geophysics</p>
                  <Select
                    className="min-w-40 col-span-2"
                    placeholder="Choose geophysics"
                    options={projectDetail?.geophysicsSuites?.map(
                      (geophysics) => ({
                        label: geophysics.name,
                        value: geophysics.id,
                      })
                    )}
                    allowClear
                    value={selectedGeophysic}
                    onChange={handleSelectGeophysics}
                  />
                  {selectedGeophysic && (
                    <Select
                      className="min-w-40 col-span-2"
                      placeholder="Choose attributes"
                      options={geophysicsAttributes.map((attribute) => ({
                        label: attribute.name,
                        value: attribute.name,
                      }))}
                      allowClear
                      mode="multiple"
                      onChange={handleSelectAttributes}
                    />
                  )}
                </div>
                <div className="items-center grid grid-cols-3">
                  <p className="font-medium col-span-1">Rock Line</p>
                  <Select
                    className="min-w-40 col-span-2"
                    placeholder="Select rock line type"
                    options={rockLineOptions}
                    value={selectedRockLineType}
                    onChange={handleRockLineTypeChange}
                    defaultValue={RockLineType.Recovery}
                  />
                </div>
              </div>

              {loggingSuiteMode === "Geotech" && isInterval && (
                <div className="font-medium">
                  From{" "}
                  <Tag color="blue">
                    {measurePointsInterval?.start?.depth.toFixed(2)}{" "}
                  </Tag>
                  to{" "}
                  <Tag color="gold">
                    {measurePointsInterval?.end?.depth.toFixed(2)}
                  </Tag>
                  <Button
                    type="primary"
                    size="small"
                    disabled={
                      !measurePointsInterval?.start &&
                      !measurePointsInterval?.end
                    }
                    onClick={() => {
                      dispatch(updateMeasurePointsInterval({}));
                    }}
                  >
                    Reset
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
        <LoggingStage
          directOCRdata={directOCRdata}
          setDirectOCRdata={setDirectOCRdata}
          loggingViewStacks={loggingViewStacks}
          onChangeText={onChangeText}
          onEnterChangeText={onEnterChangeText}
          onDblClickText={onDblClickText}
          onChange={onChange}
          directOCRdataRaw={directOCRdataRaw}
          image={image}
          setIsOpenModalOCRList={setIsOpenModalOCRList}
          refreshImageData={refreshImageData}
          fetchRecoveries={fetchRecoveries}
          onDeleteOcr={onDeleteOcr}
        />
      </div>
      {isOpenModalOCRList && (
        <ModalOcrList
          OCRdata={OCRdata}
          isOpenModalOCRList={isOpenModalOCRList}
          setIsOpenModalOCRList={setIsOpenModalOCRList}
          setOCRdata={setOCRdata}
          directOCRdata={directOCRdata}
          setDirectOCRdata={setDirectOCRdata}
        />
      )}
    </Fragment>
  );
}

export default LoggingImage;
