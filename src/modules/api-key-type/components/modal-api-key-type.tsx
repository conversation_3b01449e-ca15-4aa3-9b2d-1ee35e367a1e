import { ModalType } from "@/common/configs/app.enum";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { selectTenant } from "@/modules/tenant/redux/selectors";
import { getTenants } from "@/modules/tenant/redux/thunks";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateApiKeyType } from "../hooks/useCreateApiKeyType";
import { useDeleteApiKeyType } from "../hooks/useDeleteApiKeyType";
import { useUpdateApiKeyType } from "../hooks/useUpdateApiKeyType";
import {
  ApiKeyTypeBody,
  ApiKeyTypeBodyType,
} from "../model/schema/ api-key-type.schema";
import { TextAreaCommon } from "@/components/common/textarea-common";

export interface IModalApiKeyTypeProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalApiKeyType(props: IModalApiKeyTypeProps) {
  const { modalState, setModalState, refresh } = props;
  const { request: requestCreateApiKeyType, loading: loadingCreateApiKeyType } =
    useCreateApiKeyType();
  const { request: requestUpdateApiKeyType, loading: loadingUpdateApiKeyType } =
    useUpdateApiKeyType();
  const { request: requestDeleteApiKeyType, loading: loadingDeleteApiKeyType } =
    useDeleteApiKeyType();
  const { control, handleSubmit, setValue, getValues } =
    useForm<ApiKeyTypeBodyType>({
      resolver: zodResolver(ApiKeyTypeBody),
      defaultValues: {
        isActive: true,
        ...modalState?.detailInfo,
        keyCode: modalState?.detailInfo?.apiKeyType?.code,
      },
    });
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const onSubmit = (values: ApiKeyTypeBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateApiKeyType(values, () => {
        setModalState({ ...modalState, isOpen: false });
        toast.success("Create api key type successfully");
        refresh();
      });
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateApiKeyType(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update api key type successfully");
          refresh();
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteApiKeyType(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      () => {}
    );
  };
  const tenants = useAppSelector(selectTenant);
  const [keyword, setKeyword] = useState<string>("");
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(
      getTenants({
        isActive: true,
        keyword: keyword,
        pageSize: 1000,
      })
    );
  }, [keyword]);

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this api key?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the api
            key
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteApiKeyType}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update an api key type"
              : "Create an api key type"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (errors) => {
              console.log(errors);
            })}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Code"
              name="code"
              placeholder="Enter code"
              control={control}
            />
            <InputTextCommon
              label="Prefix"
              name="prefix"
              placeholder="Enter prefix"
              control={control}
            />
            <TextAreaCommon
              label="Description"
              name="description"
              placeholder="Enter description"
              control={control}
            />
            <ToogleCommon control={control} name="isActive" label="Is Active" />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={loadingCreateApiKeyType || loadingUpdateApiKeyType}
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update Api Key Type"
                  : "Create Api Key Type"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
