"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { IPaginationResponse } from "@/common/interfaces/response/IPaginationResponse";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import { KeyCode } from "@/modules/tenant/const/enum.tenant";
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import type { TableColumnsType } from "antd";
import { Tag } from "antd";
import { debounce } from "lodash";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useState } from "react";
import { toast } from "react-toastify";
import { getApiKeyType } from "../redux/thunks";
import { ModalApiKeyType } from "./modal-api-key-type";
const TableApiKeyType: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });
  const apiKeyType = useAppSelector((state) => state.apiKeyType);

  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: apiKeyType?.result?.items ?? [],
      reduxTablePagination: {
        total: apiKeyType?.result?.pagination.total ?? 0,
      } as IPaginationResponse,
      requestState: apiKeyType?.status ?? RequestState.idle,
      getDataAction: getApiKeyType,
      filter: {
        page: 1,
        pageSize: 10,
      },
    });

  //STATE
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });

  //table
  const columns: TableColumnsType<any> = [
    {
      title: "Account",
      dataIndex: "tenant",
      key: "tenant",
      render(value, index) {
        return <p key={index}>{value?.tenancyName}</p>;
      },
    },
    {
      title: "Key",
      dataIndex: "key",
      key: "key",
      render(value, index) {
        return (
          <div className="flex items-center gap-2" key={index}>
            <p>{value}</p>
            <CopyOutlined
              className="cursor-pointer hover:text-primary"
              onClick={() => {
                navigator.clipboard.writeText(value);
                toast.success("Copied to clipboard", {
                  position: "top-center",
                  autoClose: 500,
                });
              }}
            />
          </div>
        );
      },
    },
    {
      title: "Key Code",
      dataIndex: "apiKeyType",
      key: "apiKeyType",
      render(value, record, index) {
        switch (value?.code) {
          case KeyCode.UploadImage:
            return <p key={index}>Upload Image</p>;
          case KeyCode.DownloadImage:
            return <p key={index}>Download Image</p>;
          default:
            return <p key={index}>Unknown</p>;
        }
      },
    },
    {
      title: "Expires On",
      dataIndex: "expiresAt",
      key: "expiresAt",
      render(value, record, index) {
        if (!record?.isUnlimited) {
          const date = new Date(value);
          const formattedDate = date.toLocaleString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
          });
          return <p key={index}>{formattedDate}</p>;
        } else {
          return <Tag color="purple">Unlimited</Tag>;
        }
      },
    },

    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      render(value, index) {
        return (
          <Tag key={index} color={value ? "green" : "red"}>
            {value ? "Active" : "Inactive"}
          </Tag>
        );
      },
    },

    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },
  ];

  const updateSearchParams = useCallback(
    debounce((keyword) => {
      const params = new URLSearchParams(queries);
      keyword ? params.set("tenantName", keyword) : params.delete("tenantName");
      params.set("page", "1");
      router.replace(`${window.location.pathname}?${params.toString()}`);
    }, 300),
    [queries, router]
  );
  const fontSize = useAppSelector((state) => state.user.fontSize);
  return (
    <>
      {modalState.isOpen && (
        <ModalApiKeyType
          refresh={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold">Api Key Type</p>
        <hr />
        <div className="">
          <div className="grid grid-cols-2 gap-2">
            <div className="px-5 py-2 rounded-lg flex items-center gap-2 h-[38px bg-white border">
              <IconSearch />
              <input
                type="text"
                placeholder="Search"
                className="w-full font-normal outline-none text-primary placeholder:text-gray80"
                onChange={(e) => {
                  updateSearchParams(e.target.value);
                }}
                defaultValue={queries.tenantName}
              />
            </div>
          </div>
        </div>
        <TableCommon
          style={{
            fontSize: `${fontSize}px`,
          }}
          className="font-visby"
          rowKey={(record) => (record as any).id}
          pagination={tablePagination}
          loading={apiKeyType.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={apiKeyType.result?.items}
          footer={() => (
            <div className="justify-center my-2 ">
              <button
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  })
                }
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  Create Api Key Type
                </span>
              </button>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableApiKeyType;
