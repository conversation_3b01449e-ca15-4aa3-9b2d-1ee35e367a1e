import { useState } from "react";
import apiKeyTypeRequest from "../api/api-key-type.api";
import { ApiKeyTypeQuery } from "../interface/api-key-type.query";

export const useGetListApiKeyType = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: ApiKeyTypeQuery,
    onSuccess?: Function,
    onError?: Function
  ) => {
    setLoading(true);
    const response = await apiKeyTypeRequest.getList({
      ...params,
    });
    if (response?.state === "success") {
      setData(response.data?.items);
      setTotal(response.data?.pagination?.total);
      setLoading(false);
      onSuccess && onSuccess(response.data);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
