import { useState } from "react";
import apiKeyTypeRequest from "../api/api-key-type.api";
import { ApiKeyTypeBodyType } from "../model/schema/ api-key-type.schema";

export const useUpdateApiKeyType = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ApiKeyTypeBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    try {
      setLoading(true);
      const response = await apiKeyTypeRequest.update(params);
      if (response.state === "success") {
        onSuccess && onSuccess(response.data);
        setLoading(false);
      }
    } catch (error) {
      onError && onError(error);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
