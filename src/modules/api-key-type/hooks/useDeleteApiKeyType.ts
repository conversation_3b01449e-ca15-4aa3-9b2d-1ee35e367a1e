import { useState } from "react";
import apiKeyTypeRequest from "../api/api-key-type.api";

export const useDeleteApiKeyType = () => {
  const [loading, setLoading] = useState(false);

  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function
  ) {
    try {
      setLoading(true);
      const response = await apiKeyTypeRequest.delete(params);
      if (response.state === "success") {
        onSuccess && onSuccess(response.data);
        setLoading(false);
      } else {
      }
    } catch (error) {
      onError && onError(error);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
