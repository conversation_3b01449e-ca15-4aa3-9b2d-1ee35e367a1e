import { useState } from "react";
import { ApiKeyTypeBodyType } from "../model/schema/ api-key-type.schema";
import apiKeyTypeRequest from "../api/api-key-type.api";

export const useCreateApiKeyType = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ApiKeyTypeBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    try {
      setLoading(true);
      const response = await apiKeyTypeRequest.create(params);
      if (response.state === "success") {
        onSuccess && onSuccess(response.data);
        setLoading(false);
      } else {
      }
    } catch (error) {
      onError && onError(error);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
