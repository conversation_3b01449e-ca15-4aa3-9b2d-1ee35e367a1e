import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";
import { ApiKeyTypeQuery } from "../interface/api-key-type.query";
import { ApiKeyTypeBodyType } from "../model/schema/ api-key-type.schema";

const apiKeyTypeRequest = {
  getList: async (params: ApiKeyTypeQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ApiKeyType/GetAll`,
        params
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10)
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getApiKeyType: async (params: { Id: string }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ApiKeyType/Get?Id=${params.Id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  create: async (body: ApiKeyTypeBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/ApiKeyType/Create`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  delete: async (params: { id: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/ApiKeyType/Delete?Id=${params.id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: ApiKeyTypeBodyType) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/ApiKeyType/Update`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default apiKeyTypeRequest;
