import {
  createSlice,
  type PayloadAction,
  createSelector,
} from "@reduxjs/toolkit";
import { defaultDrillHoleViewConfig } from "../../const/drillhole-view.const";
import {
  DrillHoleAttributePoints,
  DrillHoleViewConfig,
  DrillHoleViewInfo,
} from "../../model/entities/drillhole.config";
import { EnumDrillholeView } from "../../model/enum/drillhole.enum";
import { ImageSizeEnum, ImageView } from "../../model/enum/images.enum";
import { getImages, getMultiGeologyData } from "./thunks"; // Removed getGeophysicsData as it's not used in extraReducers
import { RequestState } from "@/common/configs/app.contants";
import { DrillHoleImages } from "../../components/drillhole-view";
import { MultiLoggingInfoInterface } from "../../interface/image-logging.interface";
import { RockType } from "@/modules/logging/model/enum/logging.enum";
import { ReduxState } from "@/common/vendors/redux/store/store";
import type {
  IImageType,
  IImageSubtype,
  StructuredSelectionItem,
  SelectedNodeList,
} from "../../interface/image.interface";
import { selectAllImageTypes } from "./selectors";

const initialState: ImagesSliceState = {
  drillholeViewMode: EnumDrillholeView.NoChoice,
  viewMode: ImageView.List,
  dHViewConfigs: {
    initScale: 1,
    maxScale: 3200,
    minScale: 1,
    depthFromSize: 0.2,
    depthToSize: 0.2,
    startX: 4,
    gap: 7,
    offsetYSubstract: 1,
    pointDataWidth: 5,
    lineStrokeWidth: 0.03,
    cellWidth: 2.45,
    cellHeight: 0.5,
    strokeWidth: 0.03,
    fontSize: 0.3,
    displacementX: 0,
  },
  dHViewInfo: {
    minHeight: 0,
    maxHeight: 0,
    urlPreview: "",
    isShowTextDepth: true,
    isAlignHorizontalSpaceBetween: false,
    isHideTableInfo: false,
  },
  attributePoints: [],
  previewSize: ImageSizeEnum.MEDIUM,
  status: RequestState.idle,
  imagesOfDrillHoles: [],
  attributePointsCache: [],
  geophysicsLoading: false,
  // Geology panel UI controls
  isTextWide: false, // Default to wide text
  showCombinedResultColumn: true, // Default to show column
  // Multi geology data
  multiLoggings: [],
  getMultiLoggingStatus: RequestState.idle,
  // Geology field selection
  selectedGeologyFieldId: undefined,
  // Drillhole selection state
  selectedHoleNames: [],
  // Image filter selections
  selectedImageCategory: undefined,
  selectedNodeValues: [], // New state field
  selectedHierarchicalNodes: { selectNodes: [] }, // New hierarchical state
  allImageTypes: [], // New state field, assuming populated elsewhere or via thunk
  // Geology and Geophysics selections
  selectedGeologySuite: null,
  geologySuiteFields: [],
  selectedGeophysicsSuiteId: null,
  geophysicsSuiteAttributes: [],
  selectedGeophysicsAttributes: [],
  rulerSpacing: 1,
};

export const imagesSlice = createSlice({
  name: "images",
  initialState,
  reducers: {
    updateDrillholeView: (state, action: PayloadAction<EnumDrillholeView>) => {
      state.drillholeViewMode = action.payload;
      const config =
        action.payload === EnumDrillholeView.NoChoice
          ? defaultDrillHoleViewConfig.original
          : defaultDrillHoleViewConfig[action.payload];
      state.dHViewConfigs = config;
    },
    updateImageView: (state, action: PayloadAction<EnumDrillholeView>) => {
      // Note: This seems to update drillholeViewMode, consider renaming or refactoring if it's a bug
      state.drillholeViewMode = action.payload;
    },
    updateDHViewConfigs: (
      state,
      action: PayloadAction<Partial<DrillHoleViewConfig>>
    ) => {
      state.dHViewConfigs = {
        ...state.dHViewConfigs,
        ...action.payload,
      };
    },
    updateDHViewInfo: (
      state,
      action: PayloadAction<Partial<DrillHoleViewInfo>>
    ) => {
      state.dHViewInfo = {
        ...state.dHViewInfo,
        ...action.payload,
      };
    },
    updatePreviewSize: (state, action: PayloadAction<ImageSizeEnum>) => {
      state.previewSize = action.payload;
    },
    updateAttributePoints: (
      state,
      action: PayloadAction<DrillHoleAttributePoints[]>
    ) => {
      const payload = action.payload ?? [];
      state.attributePoints = payload;
    },
    updateImagesOfDrillHoles: (
      state,
      action: PayloadAction<DrillHoleImages[]>
    ) => {
      state.imagesOfDrillHoles = action.payload;
    },
    updateImagesOfDrillHolesByImage: (state, action: PayloadAction<any>) => {
      const drillholeId = action.payload.drillHoleId;
      const imageId = action.payload.id;
      const index = state.imagesOfDrillHoles.findIndex(
        (item) => item.drillHole.id === drillholeId
      );
      if (index !== -1) {
        const drillholeImages = state.imagesOfDrillHoles[index];
        const imageIndex = drillholeImages.list.findIndex(
          (item) => item.id === imageId
        );
        if (imageIndex !== -1) {
          drillholeImages.list[imageIndex] = action.payload;
          state.imagesOfDrillHoles[index] = drillholeImages;
        }
      }
    },
    deleteImagesOfDrillHolesByImageIds: (
      state,
      action: PayloadAction<any[]>
    ) => {
      const payload = action.payload;
      payload.forEach((imageId) => {
        const indexDrillhole = state.imagesOfDrillHoles.findIndex((item) =>
          item.list.some((image) => image.id === imageId)
        );
        if (indexDrillhole !== -1) {
          const drillholeImages = state.imagesOfDrillHoles[indexDrillhole];
          const imageIndex = drillholeImages.list.findIndex(
            (item) => item.id === imageId
          );
          if (imageIndex !== -1) {
            drillholeImages.list.splice(imageIndex, 1);
            state.imagesOfDrillHoles[indexDrillhole] = drillholeImages;
          }
        }
      });
    },
    updateAttributePointsCache: (
      state,
      action: PayloadAction<DrillHoleAttributePoints[]>
    ) => {
      state.attributePointsCache = action.payload;
    },
    // New geology panel UI control actions
    updateTextWidth: (state, action: PayloadAction<boolean>) => {
      state.isTextWide = action.payload;
    },
    updateCombinedResultColumnVisibility: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.showCombinedResultColumn = action.payload;
    },
    // Clear multi-logging data
    clearMultiLoggings: (state) => {
      state.multiLoggings = [];
      state.getMultiLoggingStatus = RequestState.idle;
    },
    // Geology field selection
    updateSelectedGeologyFieldId: (
      state,
      action: PayloadAction<number | undefined>
    ) => {
      state.selectedGeologyFieldId = action.payload;
    },
    // Drillhole selection
    updateSelectedHoleNames: (state, action: PayloadAction<string[]>) => {
      state.selectedHoleNames = action.payload;
    },
    updateSelectedImageCategory: (
      state,
      action: PayloadAction<number | undefined>
    ) => {
      state.selectedImageCategory = action.payload;
    },
    // New reducer for selectedNodeValues
    setSelectedNodeValues: (state, action: PayloadAction<string[]>) => {
      state.selectedNodeValues = action.payload;
    },
    // New reducer for hierarchical node data
    setSelectedHierarchicalNodes: (state, action: PayloadAction<SelectedNodeList>) => {
      state.selectedHierarchicalNodes = action.payload;
    },
    updateSelectedGeologySuite: (
      state,
      action: PayloadAction<{ label: string; value: string | number } | null>
    ) => {
      state.selectedGeologySuite = action.payload;
    },
    updateGeologySuiteFields: (state, action: PayloadAction<any[]>) => {
      state.geologySuiteFields = action.payload;
    },
    updateSelectedGeophysicsSuiteId: (
      state,
      action: PayloadAction<string | number | null>
    ) => {
      state.selectedGeophysicsSuiteId = action.payload;
    },
    updateGeophysicsSuiteAttributes: (state, action: PayloadAction<any[]>) => {
      state.geophysicsSuiteAttributes = action.payload;
    },
    updateSelectedGeophysicsAttributes: (
      state,
      action: PayloadAction<any[]>
    ) => {
      state.selectedGeophysicsAttributes = action.payload;
    },
    // Action to populate allImageTypes (example, might be done via thunk)
    setAllImageTypes: (state, action: PayloadAction<IImageType[]>) => {
      state.allImageTypes = action.payload;
    },

    updateRulerSpacing: (state, action: PayloadAction<number>) => {
      state.rulerSpacing = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getImages.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getImages.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      })
      .addCase(getMultiGeologyData.pending, (state) => {
        state.getMultiLoggingStatus = RequestState.pending;
      })
      .addCase(getMultiGeologyData.fulfilled, (state, action) => {
        state.getMultiLoggingStatus = RequestState.success;
        state.multiLoggings = action.payload;
      });
  },
});

export interface ImagesSliceState {
  drillholeViewMode?: EnumDrillholeView;
  viewMode?: ImageView;
  dHViewConfigs: DrillHoleViewConfig;
  dHViewInfo: DrillHoleViewInfo;
  attributePoints: DrillHoleAttributePoints[];
  attributePointsCache: DrillHoleAttributePoints[];
  previewSize: ImageSizeEnum;
  result?: any;
  status: RequestState;
  imagesOfDrillHoles: DrillHoleImages[];
  geophysicsLoading: boolean;
  // Geology panel UI controls
  isTextWide: boolean;
  showCombinedResultColumn: boolean;
  // Multi geology data
  multiLoggings: MultiLoggingInfoInterface[];
  getMultiLoggingStatus: RequestState;
  // Geology field selection
  selectedGeologyFieldId: RockType | number | undefined;
  // Drillhole selection state
  selectedHoleNames: string[];
  // Image filter selections
  selectedImageCategory?: number;
  selectedNodeValues: string[]; // Updated
  selectedHierarchicalNodes: SelectedNodeList; // New hierarchical state
  allImageTypes: IImageType[]; // Added
  // Geology and Geophysics selections
  selectedGeologySuite?: { label: string; value: string | number } | null;
  geologySuiteFields?: any[];
  selectedGeophysicsSuiteId?: string | number | null;
  geophysicsSuiteAttributes?: any[];
  selectedGeophysicsAttributes?: any[];

  rulerSpacing: number;
}

export const {
  updateDrillholeView,
  updateImageView,
  updateDHViewConfigs,
  updateDHViewInfo,
  updateAttributePoints,
  updatePreviewSize,
  updateImagesOfDrillHoles,
  updateImagesOfDrillHolesByImage,
  deleteImagesOfDrillHolesByImageIds,
  updateAttributePointsCache,
  updateTextWidth,
  updateCombinedResultColumnVisibility,
  clearMultiLoggings,
  updateSelectedGeologyFieldId,
  updateSelectedHoleNames,
  updateSelectedImageCategory,
  setSelectedNodeValues, // Added new action
  setSelectedHierarchicalNodes, // Added new hierarchical action
  updateSelectedGeologySuite,
  updateGeologySuiteFields,
  updateSelectedGeophysicsSuiteId,
  updateGeophysicsSuiteAttributes,
  updateSelectedGeophysicsAttributes,
  setAllImageTypes, // Added for populating image types
  updateRulerSpacing,
} = imagesSlice.actions;
