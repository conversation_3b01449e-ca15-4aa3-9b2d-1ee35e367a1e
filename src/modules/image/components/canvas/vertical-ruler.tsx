"use client";
import { useAppDispatch } from "@/common/vendors/redux/store/hook";
import Group from "antd/es/input/Group";
import Konva from "konva";
import { KonvaEventObject } from "konva/lib/Node";
import React, { Fragment, useEffect, useMemo, useRef, useState } from "react";
import { Circle, Layer, Line, Text } from "react-konva";
import { updateRulerSpacing } from "../../redux/imageSlice";

interface VerticalRulerProps {
  height: number;
  start: number;
  end: number;
  cursorYPosition: number;
  goToYPosition: (y: number) => void;
  setCurrentPosLayer: ({ x, y }: { x: number; y: number }) => void;
  currentPosLayer: { x: number; y: number };
}

interface Position {
  x?: number;
  y?: number;
}

const VerticalRuler: React.FC<VerticalRulerProps> = ({
  height,
  start = 0,
  end = 0,
  cursorYPosition,
  goToYPosition,
  currentPosLayer,
  setCurrentPosLayer,
}) => {
  const dispatch = useAppDispatch();
  const marks: any[] = [];
  const RULER_X_OFFSET = 80;
  const RULER_Y_OFFSET = 20;
  const markSpace = useMemo(() => {
    const markBetween = end - start;

    if (markBetween >= 100) {
      return 100;
    }
    if (markBetween >= 10) {
      return 10;
    }
    return 1;
  }, [start, end]);

  const [highlightPosition, setHighlightPosition] = useState<Position>({
    x: -1,
  });

  // Tính toán khoảng cách hợp lý giữa các dấu chia
  const calculateSpace = useMemo(() => {
    const availableSpaces = [1000, 500, 200, 100, 50, 10, 5, 2, 1, 0.5];
    let startCoordinate = 0;
    let endCoordinate = 0;
    const resonableSpace: number | undefined = availableSpaces.find(
      (availableSpace) => {
        startCoordinate = Math.floor(start / markSpace) * markSpace;
        endCoordinate = Math.ceil(end / markSpace) * markSpace;
        return height / availableSpace > endCoordinate - startCoordinate;
      }
    );

    return {
      spacing: resonableSpace ?? 1,
      startCoordinate,
      endCoordinate,
    };
  }, [height, start, end]);

  const spacing = calculateSpace.spacing;
  const longMarkLength = 12;

  for (
    let i = calculateSpace.startCoordinate;
    i <= calculateSpace.endCoordinate;
    i++
  ) {
    const y = i - calculateSpace.startCoordinate;
    if ((i - calculateSpace.startCoordinate) % markSpace === 0) {
      marks.push(
        <Line
          key={i}
          points={[
            RULER_X_OFFSET - longMarkLength,
            y * spacing + RULER_Y_OFFSET,
            RULER_X_OFFSET + 3,
            y * spacing + RULER_Y_OFFSET,
          ]}
          stroke="black"
          strokeWidth={2}
        />
      );
      const startX = RULER_X_OFFSET - longMarkLength - 4 - String(i).length * 8;
      marks.push(
        <Text
          key={`text-${i}`}
          x={startX}
          y={y * spacing - 6 + RULER_Y_OFFSET}
          text={i.toString()}
          fontSize={14}
          fill="black"
        />
      );
    }
  }
  const handleClick = (event) => {
    // Get the position of the click relative to the stage
    try {
      const { y } = event?.target?.getStage()?.getPointerPosition();
      const newY =
        calculateSpace.startCoordinate +
        (y - RULER_Y_OFFSET) / calculateSpace.spacing;
      setCurrentPosLayer({
        ...currentPosLayer,
        y: newY,
      });
      goToYPosition(newY);
    } catch (error) {
      console.error("Error when click on vertical ruler", error);
    }
  };

  const handleMouseMove = (event) => {
    try {
      const { x, y } = event?.target?.getStage()?.getPointerPosition();
      // const newY = (y - RULER_Y_OFFSET) / 2;
      setHighlightPosition({
        x: RULER_X_OFFSET,
        y: y,
      });
    } catch (error) {
      console.error("Error when move mouse on vertical ruler", error);
    }
  };

  const handleMouseOut = () => {
    setHighlightPosition({
      x: -1,
      y: -1,
    });
  };

  useEffect(() => {
    dispatch(updateRulerSpacing(markSpace ?? 1));
  }, [markSpace]);

  return (
    <Fragment>
      <Layer draggable={false} scaleX={1} scaleY={1} fill={null}>
        {highlightPosition.x !== -1 && highlightPosition.y !== -1 && (
          <Group>
            <Text
              x={(highlightPosition.x || 0) + 10}
              y={(highlightPosition.y || 0) + 4}
              text={(
                calculateSpace.startCoordinate +
                ((highlightPosition.y ?? 0) - RULER_Y_OFFSET) /
                  calculateSpace.spacing
              ).toFixed(2)}
              fontSize={16}
              fontStyle="bold"
              fill="#4A79A8"
            />
          </Group>
        )}
        <Line
          points={[
            RULER_X_OFFSET,
            RULER_Y_OFFSET,
            RULER_X_OFFSET,
            (calculateSpace.endCoordinate - calculateSpace.startCoordinate) *
              calculateSpace.spacing +
              RULER_Y_OFFSET,
          ]}
          stroke="black"
          strokeWidth={6}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseOut}
          onClick={handleClick}
        />
        {marks}
      </Layer>

      {cursorYPosition >= 0 && (
        <Layer>
          <Line
            points={[
              RULER_X_OFFSET,
              (cursorYPosition -
                calculateSpace.startCoordinate +
                RULER_Y_OFFSET / calculateSpace.spacing) *
                calculateSpace.spacing,
              RULER_X_OFFSET + 40,
              (cursorYPosition -
                calculateSpace.startCoordinate +
                RULER_Y_OFFSET / calculateSpace.spacing) *
                calculateSpace.spacing,
            ]}
            stroke="#2D669B"
            strokeWidth={3}
            dash={[4, 4]}
          />
          <Text
            x={RULER_X_OFFSET + 10}
            y={
              (cursorYPosition - calculateSpace.startCoordinate) *
                calculateSpace.spacing +
              RULER_Y_OFFSET +
              4
            }
            text={cursorYPosition.toFixed(2)}
            fontSize={16}
            fontStyle="bold"
            fill="black"
          />
        </Layer>
      )}
    </Fragment>
  );
};

export default React.memo(VerticalRuler);
