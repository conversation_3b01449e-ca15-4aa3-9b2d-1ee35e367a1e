import { useAppDispatch } from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { InputNumberCommon } from "@/components/common/input-number";
import { SelectCommon } from "@/components/common/select-common";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import { useQueryImageSubType } from "@/modules/image-type/hooks/useGetQueryImageSubType";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import { useGetListProject } from "@/modules/projects/hooks";
import { useGetListProspect } from "@/modules/prospect/hooks/useGetListProspect";
import { Form, Spin } from "antd";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useUpdateImage } from "../hooks/useUpdateImage";
import { updateImagesOfDrillHolesByImage } from "../redux/imageSlice/image.slice";

export interface IModalEditImageProps {
  modalState: any;
  setModalState: any;
}

export function ModalEditImage({
  modalState,
  setModalState,
}: IModalEditImageProps) {
  const record = modalState?.detailInfo;
  const { control, handleSubmit, setValue, watch, reset } = useForm<any>({
    // defaultValues: {
    //   ...record,
    //   projectId: record?.project?.id,
    //   drillHoleId: record?.drillHole?.id,
    //   prospectId: record?.prospect?.id,
    // },
  });
  useEffect(() => {
    reset({
      ...record,
      projectId: record?.project?.id,
      drillHoleId: record?.drillHole?.id,
      prospectId: record?.prospect?.id,
      imageTypeId: record?.imageType?.id,
      imageSubTypeId: record?.imageSubtype?.id,
    });
  }, [record]);
  const { request: updateImage, loading: loadingUpdateImage } =
    useUpdateImage();
  const dispatch = useAppDispatch();

  const onSubmit = (values: any) => {
    const { boundingBox, boundingRows, status, id, ...restRecord } = record;
    const {
      depthFrom,
      depthTo,
      type,
      drillHoleId,
      projectId,
      prospectId,
      imageTypeId,
      imageSubTypeId,
      ...restValues
    } = values;
    updateImage(
      {
        id,
        boundingBox,
        boundingRows,
        status,
        depthFrom,
        depthTo,
        projectId,
        drillHoleId,
        type,
        prospectId,
        imageTypeId,
        imageSubTypeId,
      },
      () => {
        setModalState({
          ...modalState,
          isOpen: false,
        });
        dispatch(updateImagesOfDrillHolesByImage(values));
      },
      (error) => {
        toast.error(error);
      }
    );
  };
  const projectId = watch("projectId");
  const {
    data: projects,
    request: getListProjects,
    loading: loadingListProject,
  } = useGetListProject();
  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const [keywordProject, setKeywordProject] = useState("");
  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  useEffect(() => {
    getListProjects({
      skipCount: 0,
      maxResultCount: maxResultCountProject,
      keyword: keywordProject,
    });
  }, [maxResultCountProject, keywordProject]);
  const {
    data: drillholes,
    request: getListDrillhole,
    loading: loadingListDrillhole,
  } = useGetListDrillhole();
  const [maxResultCountDrillhole, setMaxResultCountDrillhole] = useState(100);
  useEffect(() => {
    getListDrillhole({
      skipCount: 0,
      maxResultCount: maxResultCountDrillhole,
      projectIds: [projectId],
    });
  }, [setMaxResultCountDrillhole, projectId]);
  const { data: listProspect, request: getListProspect } = useGetListProspect();
  const [keywordProspect, setKeywordProspect] = useState("");
  const [maxResultCountProspect, setMaxResultCountProspect] = useState(10);
  const handleScrollProspect = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProspect(maxResultCountProspect + 10);
    }
  };
  useEffect(() => {
    if (projectId) {
      getListProspect({
        keyword: keywordProspect,
        maxResultCount: maxResultCountProspect,
        skipCount: 0,
        projectIds: [projectId],
      });
    }
  }, [projectId, keywordProspect, maxResultCountProspect]);
  const { data: imageTypes } = useQueryImageType();
  const imageCategoryOptions = useMemo(
    () =>
      imageTypes?.data?.items.map((item) => ({
        label: item.name,
        value: item.id,
      })) || [],
    [imageTypes?.data?.items]
  );
  const imageTypeId = watch("imageTypeId");

  const { data: imageSubTypes, setSearchParams: setSearchParamsImageSubType } =
    useQueryImageSubType();
  const imageSubTypeOptions = useMemo(() => {
    if (imageTypeId) {
      return (
        imageSubTypes?.data?.items.map((item) => ({
          label: item.name,
          value: item.id,
        })) || []
      );
    }
    return [];
  }, [imageSubTypes?.data?.items, imageTypeId]);
  useEffect(() => {
    if (imageTypeId) {
      setSearchParamsImageSubType({
        imageTypeId: imageTypeId,
      });
    }
  }, [imageTypeId]);

  return (
    <div>
      <p className="font-bold text-30-40 uppercase text-center font-visby">
        Edit Image
      </p>

      <Form onFinish={handleSubmit(onSubmit)} className="flex flex-col gap-3">
        <SelectCommon
          isRequired
          name="projectId"
          onPopupScroll={handleScrollProject}
          onChange={(value) => {
            setValue("drillHoleId", null);
            getListDrillhole({
              skipCount: 0,
              maxResultCount: maxResultCountDrillhole,
              projectIds: [value],
            });
          }}
          control={control}
          options={projects?.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
          label="Project"
          filterOption={false}
          showSearch
          notFoundContent={
            loadingListProject ? <Spin size="small" /> : <>No data</>
          }
          onSearch={(value) => {
            getListProjects({
              maxResultCount: maxResultCountProject,
              skipCount: 0,
              keyword: value,
            });
          }}
        />
        <SelectCommon
          name="prospectId"
          isRequired
          options={(listProspect ?? []).map((d) => ({
            label: d.name,
            value: d.id,
          }))}
          showSearch
          onPopupScroll={handleScrollProspect}
          onSearch={(value) => {
            setKeywordProspect(value);
          }}
          onClear={() => {
            setKeywordProspect("");
          }}
          onBlur={() => {
            setKeywordProspect("");
          }}
          searchValue={keywordProspect}
          filterOption={false}
          control={control}
          label="Prospect"
          placeholder="Select a prospect"
          allowClear
        />
        <SelectCommon
          name="drillHoleId"
          isRequired
          control={control}
          options={drillholes?.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
          label="Drillhole"
          filterOption={false}
          showSearch
          notFoundContent={
            loadingListDrillhole ? <Spin size="small" /> : <>No data</>
          }
          placeholder="Select Drillhole"
          onSearch={(value) => {
            getListDrillhole({
              maxResultCount: maxResultCountDrillhole,
              skipCount: 0,
              keyword: value,
              projectIds: [projectId],
            });
          }}
        />
        <InputNumberCommon
          name="depthFrom"
          control={control}
          label="Depth From"
        />
        <InputNumberCommon name="depthTo" control={control} label="Depth To" />
        <SelectCommon
          control={control}
          options={imageCategoryOptions}
          placeholder="Image Type"
          onChange={(value, option) => {
            setValue("imageType", {
              id: option.value,
              name: option.label,
            });
          }}
          allowClear
          name="imageTypeId"
          label="Image Type"
        />
        {imageSubTypeOptions.length > 0 && watch("imageTypeId") && (
          <SelectCommon
            control={control}
            options={imageSubTypeOptions}
            placeholder="Image Subtype"
            allowClear
            name="imageSubTypeId"
            label="Image Subtype"
            onChange={(value, option) => {
              setValue("imageSubtype", {
                id: option.value,
                name: option.label,
              });
            }}
          />
        )}

        <div className="flex justify-end gap-2">
          <ButtonCommon
            onClick={() => {
              setModalState({
                ...modalState,
                isOpen: false,
              });
            }}
            className="btn btn-sm"
          >
            No
          </ButtonCommon>
          <ButtonCommon
            loading={loadingUpdateImage}
            onClick={handleSubmit(onSubmit)}
            className="btn btn-sm bg-primary text-white hover:bg-primary"
          >
            Yes
          </ButtonCommon>
        </div>
      </Form>
    </div>
  );
}
