import { openDB, IDBPDatabase } from "idb";
import { ImageQuery } from "../interface/image.interface";

interface CacheEntry {
  data: any;
  timestamp: number;
}

const DB_NAME = "drillhole-image-cache";
const STORE_NAME = "image-responses";
const CACHE_EXPIRY = 3 * 60 * 1000; // 3 minutes in milliseconds

export const useImageCache = () => {
  let db: IDBPDatabase | null = null;

  const initDB = async (): Promise<IDBPDatabase> => {
    if (db) return db;

    try {
      db = await openDB(DB_NAME, 1, {
        upgrade(database) {
          if (!database.objectStoreNames.contains(STORE_NAME)) {
            database.createObjectStore(STORE_NAME);
          }
        },
      });
      return db;
    } catch (error) {
      console.error("Failed to initialize IndexedDB:", error);
      throw error;
    }
  };

  const generateCacheKey = (params: Partial<ImageQuery>): string => {
    const keyParams = {
      drillHoleNames: params.drillHoleNames?.join(",") || "",
      skipCount: params.skipCount || 0,
      maxResultCount: params.maxResultCount || 0,
      imageTypeId: params.imageTypeId || "",
      imageSubtypeId: params.imageSubtypeId || "",
      imageCategory: params.imageCategory || "",
    };
    return JSON.stringify(keyParams);
  };

  const getCachedData = async (
    params: Partial<ImageQuery>
  ): Promise<any | null> => {
    try {
      const database = await initDB();
      const key = generateCacheKey(params);
      const entry = (await database.get(STORE_NAME, key)) as
        | CacheEntry
        | undefined;

      if (!entry) return null;

      const now = Date.now();
      if (now - entry.timestamp > CACHE_EXPIRY) {
        // Cache expired, remove it
        await database.delete(STORE_NAME, key);
        return null;
      }

      return entry.data;
    } catch (error) {
      console.error("Error retrieving from cache:", error);
      return null;
    }
  };

  const setCachedData = async (
    params: Partial<ImageQuery>,
    data: any
  ): Promise<void> => {
    try {
      const database = await initDB();
      const key = generateCacheKey(params);
      const entry: CacheEntry = {
        data,
        timestamp: Date.now(),
      };
      await database.put(STORE_NAME, entry, key);
    } catch (error) {
      console.error("Error storing in cache:", error);
    }
  };

  const clearExpiredCache = async (): Promise<void> => {
    try {
      const database = await initDB();
      const now = Date.now();
      const allKeys = await database.getAllKeys(STORE_NAME);

      for (const key of allKeys) {
        const entry = (await database.get(STORE_NAME, key)) as CacheEntry;
        if (now - entry.timestamp > CACHE_EXPIRY) {
          await database.delete(STORE_NAME, key);
        }
      }
    } catch (error) {
      console.error("Error clearing expired cache:", error);
    }
  };

  const clearAllCache = async (): Promise<void> => {
    try {
      const database = await initDB();
      const allKeys = await database.getAllKeys(STORE_NAME);

      // Delete all entries regardless of expiration status
      for (const key of allKeys) {
        await database.delete(STORE_NAME, key);
      }

      console.log("All cache entries cleared successfully");
    } catch (error) {
      console.error("Error clearing all cache entries:", error);
    }
  };

  return {
    getCachedData,
    setCachedData,
    clearExpiredCache,
    clearAllCache,
  };
};
